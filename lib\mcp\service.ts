// ./lib/mcp/service.ts
import "server-only";
import type {
  ClientRequest,
} from "@modelcontextprotocol/sdk/types.js";
import {
  CompatibilityCallToolResultSchema,
  GetPromptResultSchema,
  ListPromptsResultSchema,
  ListResourcesResultSchema,
  ListResourceTemplatesResultSchema,
  ReadResourceResultSchema,
  ListToolsResultSchema,
} from "@modelcontextprotocol/sdk/types.js";
import type { RequestOptions } from "@modelcontextprotocol/sdk/shared/protocol.js";
import { SSEClientTransport} from "@modelcontextprotocol/sdk/client/sse.js";
import { StdioClientTransport} from "@modelcontextprotocol/sdk/client/stdio.js";
import { StreamableHTTPClientTransport } from "@modelcontextprotocol/sdk/client/streamableHttp.js";
import { Client } from "@modelcontextprotocol/sdk/client/index.js";
import { z } from "zod";
import type { MCPConnectionOptions } from "./load.config";

export enum ConnectionType {
  SSE = "sse",
  STDIO = "stdio",
  WEBSOCKET = "websocket",
  STREAM = "streamable-http",
  UNKNOWN = "unknown",
}

type SupportedTransports =
  | StdioClientTransport
  | SSEClientTransport
  | StreamableHTTPClientTransport;

export class MCPService {
  private clients: Map<string, { client: Client; transport: SupportedTransports }> = new Map();

  constructor(private configs: MCPConnectionOptions[] |null) {}

  async connectAll(): Promise<void> {
    if(this.configs == null) throw new Error("No configs found");
    for (const config of this.configs) {
      const { id, transportType, command, args, sseUrl, env } = config;

      console.log(id, transportType, command, args, sseUrl, env);
      if (this.clients.has(id)) {
        console.warn(`Client with ID "${id}" already connected.`);
        continue;
      }

      let transport: SupportedTransports;
      const client = new Client({ name: id, version: "1.0.0" });

      switch (transportType) {
        case ConnectionType.SSE:
          if (!sseUrl)
            throw new Error(`sseUrl is required for SSE transport in config "${id}"`);
          transport = new SSEClientTransport(new URL(sseUrl));
          break;
        case ConnectionType.STREAM:
          if (!sseUrl)
            throw new Error(`sseUrl is required for Streamable HTTP transport in config "${id}"`);
          transport = new StreamableHTTPClientTransport(new URL(sseUrl));
          break;
        case ConnectionType.STDIO:
          if (!command)
            throw new Error(`command is required for STDIO transport in config "${id}"`);
          transport = new StdioClientTransport({ command, args, env });
          break;
        case ConnectionType.WEBSOCKET:
          throw new Error(`WebSocket transport not implemented yet for config "${id}"`);
        default:
          throw new Error(`Unsupported transport type: ${transportType} in config "${id}"`);
      }

      await client.connect(transport);
      this.clients.set(id, { client, transport });
      console.log(`Connected to MCP server: ${id}`);
    }
    console.log("All MCP servers connected.");
  }

  async disconnectAll(): Promise<void> {
    for (const [id, { client }] of this.clients.entries()) {
      await client.close();
      console.log(`Disconnected from MCP server: ${id}`);
    }
    this.clients.clear();
  }

  getClientById(id: string): Client | undefined {
    const entry = this.clients.get(id);
    return entry ? entry.client : undefined;
  }

  async makeRequestForClient<T extends z.ZodTypeAny>(
    clientId: string,
    request: ClientRequest,
    schema: T,
    options?: RequestOptions
  ): Promise<z.infer<T>> {
    const client = this.getClientById(clientId);
    if (!client) {
      throw new Error(`Client with ID "${clientId}" not found.`);
    }

    console.log(`Making request to MCP server: ${clientId}`);
    try {
      const mcpRequestOptions: RequestOptions = {
        signal: options?.signal,
        resetTimeoutOnProgress: options?.resetTimeoutOnProgress,
        timeout: options?.timeout ?? 30000,
        maxTotalTimeout: options?.maxTotalTimeout ?? 60000,
      };

      const response = await client.request(request, schema, mcpRequestOptions);
      return response;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new Error(errorMessage);
    }
  }

  // === Unified API methods accepting clientId ===

  async listResources(clientId: string, cursor: string): Promise<z.infer<typeof ListResourcesResultSchema>> {
    return await this.makeRequestForClient(
      clientId,
      { method: "resources/list", params: cursor ? { cursor } : {} },
      ListResourcesResultSchema
    );
  }

  async readResource(clientId: string, uri: string): Promise<z.infer<typeof ReadResourceResultSchema>> {
    return await this.makeRequestForClient(
      clientId,
      { method: "resources/read", params: { uri } },
      ReadResourceResultSchema
    );
  }

  async subscribeToResource(clientId: string, uri: string) {
    return await this.makeRequestForClient(
      clientId,
      { method: "resources/subscribe", params: { uri } },
      z.object({})
    );
  }

  async unsubscribeFromResource(clientId: string, uri: string) {
    return await this.makeRequestForClient(
      clientId,
      { method: "resources/unsubscribe", params: { uri } },
      z.object({})
    );
  }

  async listPrompts(clientId: string, cursor: string): Promise<z.infer<typeof ListPromptsResultSchema>> {
    return await this.makeRequestForClient(
      clientId,
      { method: "prompts/list", params: cursor ? { cursor } : {} },
      ListPromptsResultSchema
    );
  }

  async getPrompt(clientId: string,name: string, args: Record<string, any> = {}): Promise<z.infer<typeof GetPromptResultSchema>> {
    return await this.makeRequestForClient(
      clientId,
      { method: "prompts/get", params: { name, arguments: args } },
      GetPromptResultSchema
    );
  }

  async listTools(clientId: string): Promise<z.infer<typeof ListToolsResultSchema>> {
    return await this.makeRequestForClient(
        clientId,
        { method: "tools/list" },
        ListToolsResultSchema
    );
  }

  async callTool(clientId: string, name: string, params: Record<string, any>): Promise<z.infer<typeof CompatibilityCallToolResultSchema>> {
    try {
      return await this.makeRequestForClient(
        clientId,
        {
          method: "tools/call",
          params: {
            name,
            arguments: params,
            _meta: {
              progressToken: Date.now(),
            },
          },
        },
        CompatibilityCallToolResultSchema
      );
    } catch (e) {
      let errorMessage: string;
      if (e instanceof Error) {
        errorMessage = e.message;
      } else if (typeof e === "string") {
        errorMessage = e;
      } else {
        errorMessage = String(e);
      }

      return {
        content: [
          {
            type: "text",
            text: errorMessage || "An error occurred",
          },
        ],
        isError: true,
      };
    }
  }
}