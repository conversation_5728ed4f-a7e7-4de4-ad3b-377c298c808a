# Процесс восстановления пароля

## 1. Введение
Данный документ описывает процесс восстановления пароля, включая клиентскую и серверную части, обработку состояний и основные компоненты страницы восстановления пароля.

## 2. Страница Восстановления Пароля

### 2.1 Основные компоненты
- **URL:** `/forgot-password`
- **Метод:** `POST`
- **Ключевые элементы интерфейса:**
    - Поле ввода `email`
    - Кнопка `Send Recovery Email`
    - Ссылка на страницу логина `/login`

### 2.2 Обработка состояний

| Состояние       | Действие                                          |
|-----------------|---------------------------------------------------|
| `idle`          | Отображение формы восстановления пароля           |
| `failed`        | Вывод ошибки `Invalid email address!`             |
| `invalid_data`  | Вывод ошибки `Failed validating your submission!` |
| `success`       | Отображение сообщения о том, что письмо отправлено |

## 3. Технические детали реализации

### 3.1 Клиентская часть

#### **Технологии:**
- React
- Next.js
- TypeScript

#### **Основные функции:**
- `useActionState` — управление состоянием формы
- `useEffect` — обработка ответов сервера
- `toast` — отображение уведомлений об ошибках
- `useRouter` — навигация

#### **Использование `useEffect`**
`useEffect` отслеживает состояние формы (`state.status`) и выполняет следующие действия:
- При `failed` или `invalid_data` отображает соответствующее уведомление через `toast`
- При `success`:
    - Отображает сообщение о том, что письмо для восстановления пароля было отправлено
    - Перенаправляет пользователя на страницу логина

### 3.2 Серверная часть

#### **Обработчик:** `../actions (функция forgotPassword)`
#### **Механизм работы:**
1. Получение данных формы через `FormData`
2. Вызов функции `forgotPassword` с переданными данными
3. Обработка результата:
    - При успехе: отправка email для восстановления пароля
    - При ошибке: отображение соответствующего сообщения

## 4. Пример использования

### 4.1 HTML-структура страницы восстановления пароля
```html
<div className="w-full max-w-md">
  <h3>Forgot Password</h3>
  <p>Enter your email address to reset your password</p>
  <AuthForm>
    <SubmitButton>Send Recovery Email</SubmitButton>
    <p>
      Remembered your password? 
      <Link href="/login">Sign in</Link>
    </p>
  </AuthForm>
</div>
```

### 4.2 Обработчик формы (`handleSubmit`)
```tsx
const handleSubmit = (formData: FormData) => {
  setEmail(formData.get('email') as string);
  formAction(formData);
};
```

## 5. Особенности реализации
- **Сохранение email между попытками восстановления пароля:**
    - Используется `useState(email)`, передаваемый в `AuthForm`.
- **Обновление страницы после успешной отправки email для восстановления пароля:**
    - После успешного выполнения запрос отправляется уведомление о том, что письмо было отправлено, и пользователь перенаправляется на страницу логина.
- **Навигация:**
    - В случае успешного восстановления пароля предоставляется ссылка на страницу входа `/login`.

## 6. Последовательность восстановления пароля

1. **Пользователь переходит на страницу восстановления пароля** по адресу `/forgot-password`.
2. **Заполняет поле email** в форме и нажимает кнопку `Send Recovery Email`.
3. **Клиентская часть:** форма отправляет запрос на сервер с email пользователя.
4. **Серверная часть:**
    - Сервер проверяет, существует ли указанный email в базе данных.
    - Если email существует, отправляется письмо с инструкциями по восстановлению пароля.
    - Если email не найден, возвращается ошибка `Invalid email address!`.
5. **Клиентская часть:** после получения ответа от сервера:
    - При успешной отправке письма отображается уведомление о том, что письмо отправлено.
    - При ошибке возвращается соответствующее сообщение о неверном email.
6. **Пользователь переходит на страницу логина** по ссылке и использует восстановленный пароль для входа.

---
