'use client';
import React from 'react'; // Explicitly import React
import { useEffect, useState } from 'react';
import { useCookies } from 'next-client-cookies';

const locales = ['en', 'ru', 'de']; // Поддерживаемые локали

export default function LanguageSwitcher() {
    const [locale, setLocale] = useState('en');
    const cookies = useCookies();
    useEffect(() => {
        const currentLocale = cookies.get('NEXT_LOCALE') || 'en';
        setLocale(currentLocale as string);
    }, []);

    const handleChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
        const selectedLocale = event.target.value;
        cookies.set('NEXT_LOCALE', selectedLocale);
        setLocale(selectedLocale);
        window.location.reload(); // Перезагрузка страницы для применения новой локали
    };

    return (
        <select value={locale} onChange={handleChange}>
            {locales.map((loc) => (
                <option key={loc} value={loc}>
                    {loc.toUpperCase()}
                </option>
            ))}
        </select>
    );
}
