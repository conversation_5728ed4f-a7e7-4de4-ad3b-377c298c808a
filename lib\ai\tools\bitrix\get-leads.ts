import { z } from 'zod';
import {
  getMcpClient,
  bitrixDomain,
  bitrixWebhookToken,
  callMcpTool,
  processMcpResponse,
} from './mcp-client';
import type { Session } from 'next-auth';
import { type DataStreamWriter, tool } from 'ai';

interface PerformRagQueryProps {
  session: Session;
  dataStream: DataStreamWriter;
}

export const getLeadsSchema = z.object({
  filter: z
    .string()
    .optional()
    .describe(
      'JSON-строка с фильтром для лидов Bitrix24. Например: \'{"STATUS_ID": "NEW"}\' или \'{"PHONE": "+71234567890"}\'',
    ),
});

export const callMcpGetLeads = ({
  session,
  dataStream,
}: PerformRagQueryProps) =>
  tool({
    description:
      'Получить список лидов из Bitrix24 CRM. Можно использовать фильтр в формате JSON для поиска по определенным полям (например, по статусу или телефону).',
    parameters: getLeadsSchema,
    execute: async ({ filter }) => {
      console.log(`[AI Tool] Requested 'getLeads' with filter: ${filter}`);
      const mcpResult = await _callMcpGetLeads({ filter });
      // biome-ignore lint/suspicious/noImplicitAnyLet: <explanation>
      let toolResult;
      if (mcpResult.success && mcpResult.data) {
        toolResult = {
          success: true,
          data: JSON.parse(mcpResult.data),
        };
        // console.log(
        //   `[AI Tool] 'getLeads' successful. Result for AI:`,
        //   toolResult,
        // );
      } else {
        toolResult = { success: false, error: mcpResult.error };
        console.error(`[AI Tool] 'getLeads' failed. Error for AI:`, toolResult);
      }

      dataStream.writeData({ type: 'finish', content: '' });

      return toolResult;
    },
  });

async function _callMcpGetLeads({
  filter,
}: z.infer<typeof getLeadsSchema>): Promise<{
  success: boolean;
  data?: string;
  error?: string;
}> {
  const mcpClient = getMcpClient();
  if (!mcpClient) {
    const errorMsg =
      'MCP client not configured (MCP_SERVER_URL environment variable is missing). Cannot call Bitrix24.';
    console.error(errorMsg);
    return { success: false, error: errorMsg };
  }
  try {
    // Добавляем параметры Bitrix24 в запрос
    const args: Record<string, unknown> = {};

    // Добавляем фильтр, если он есть
    if (filter) {
      args.filter = filter;
    }

    // Добавляем параметры Bitrix24, если они есть
    if (bitrixDomain && bitrixWebhookToken) {
      args.domain = bitrixDomain;
      args.token = bitrixWebhookToken;
    }

    console.log(
      `[MCP Client] Calling getLeads with filter: ${filter || 'none'} and Bitrix domain: ${bitrixDomain || 'none'}`,
    );

    // Вызываем MCP инструмент через общую функцию из mcp-client
    const response: unknown = await callMcpTool('getLeads', args);
    // console.log(
    //   '[MCP Client] Received response:',
    //   JSON.stringify(response, null, 2),
    // );

    // Обрабатываем ответ через отдельную функцию
    return processMcpResponse(response);
  } catch (error: unknown) {
    console.error('[MCP Client] Error calling MCP getLeads:', error);
    const message =
      error instanceof Error ? error.message : 'Unknown error during MCP call';
    return { success: false, error: `MCP Client Error: ${message}` };
  }
}
