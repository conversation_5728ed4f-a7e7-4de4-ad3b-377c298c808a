'use client';

import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useWindowSize } from 'usehooks-ts';

import { SidebarToggle } from '@/components/sidebar-toggle';
import { Button } from '@/components/ui/button';
import { PlusIcon } from './icons';
import { useSidebar } from './ui/sidebar';
import { memo } from 'react';
import { Tooltip, TooltipContent, TooltipTrigger } from './ui/tooltip';
import { VisibilityType } from './visibility-selector';
import HorizontalLanguageSwitcher from '@/components/hor-language-switcher';

export function ChatHeader() {
  const router = useRouter();
  const { open } = useSidebar();

  const { width: windowWidth } = useWindowSize();

  return (
    <header className="flex flex-row sticky top-0 bg-transparent py-1.5 px-2 md:px-2 gap-2">
      <SidebarToggle />
      <div className="inline-flex ml-auto">
        <HorizontalLanguageSwitcher />
      </div>
    </header>
  );
}

// export const ChatHeader = memo(PureChatHeader, (prevProps, nextProps) => {
//   return prevProps.selectedModelId === nextProps.selectedModelId;
// });
