// lib/rag/cloudflare/cfautorag.client.ts

import { AiSearchRequest, AiSearchResponse } from "@/lib/rag/cloudflare/cfaisearch.types";

export class CloudflareAiClient {
    constructor(
        private accountId: string,
        private autoragName: string,
        private apiToken: string
    ) {}

    async search(request: AiSearchRequest): Promise<AiSearchResponse> {
        const url = `https://api.cloudflare.com/client/v4/accounts/${this.accountId}/autorag/rags/${this.autoragName}/ai-search`;

        const response = await fetch(url, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${this.apiToken}`
            },
            body: JSON.stringify(request)
        });

        if (!response.ok) {
            throw new Error(`CloudfНlare AI search failed: ${response.statusText}`);
        }

        const data = await response.json();
        return data as AiSearchResponse;
    }
}
