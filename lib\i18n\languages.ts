// locales.ts

// Создаем перечисление для языков
export enum LocaleCode {
    EN = 'en',
    RU = 'ru',
    DE = 'de',
}

// Интерфейс для локали
export interface Locale {
    code: LocaleCode;
    flag: string;
    alt: string;
}

// Массив локалей
const locales: Locale[] = [
    { code: LocaleCode.EN, flag: '/images/b_5_en.png', alt: 'English' },
    { code: LocaleCode.RU, flag: '/images/b_5_ru.png', alt: 'Русский' },
    { code: LocaleCode.DE, flag: '/images/b_5_de.png', alt: 'Deutsch' },
];

// Преобразуем массив локалей в объект для быстрого поиска по коду
const localeDictionary = locales.reduce((acc, locale) => {
    acc[locale.code] = locale;
    return acc;
}, {} as Record<LocaleCode, Locale>);

// Функция для получения локали по коду
export const getLocaleByCode = (code: LocaleCode): Locale | undefined => {
    return localeDictionary[code];
};

// Функция для получения всех локалей
export const getAllLocales = (): Locale[] => {
    return locales;
};

export const isValidLocaleCode = (code: string): code is LocaleCode => {
    return Object.values(LocaleCode).includes(code as LocaleCode);
};
