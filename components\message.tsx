'use client';

import type { UIMessage } from 'ai';
import cx from 'classnames';
import { AnimatePresence, motion } from 'framer-motion';
import { memo, useState } from 'react';
import type { Vote } from '@/lib/db/schema';
import { DocumentToolCall, DocumentToolResult } from './document';
import {
  BCA_logo,
  InfoIcon,
  LoaderIcon,
  PencilEditIcon,
  SparklesIcon,
} from './icons';
import { Markdown } from './markdown';
import { MessageActions } from './message-actions';
import { PreviewAttachment } from './preview-attachment';
import { Weather } from './weather';
import equal from 'fast-deep-equal';
import { cn } from '@/lib/utils';
import { Button } from './ui/button';
import { MessageEditor } from './message-editor';
import { DocumentPreview } from './document-preview';
import { MessageReasoning } from './message-reasoning';
import type { UseChatHelpers } from '@ai-sdk/react';
import { useTranslations } from 'next-intl';
import { Tooltip, TooltipContent, TooltipTrigger } from './ui/tooltip';
import { ErrorMessageWithRetry } from './error-message-with-retry';
import { LeadsDataTable } from './leads-data-table';
import { PotentialLeadsTable } from './potential-leads-table';
import { PotentialLeadDetails } from './potential-lead-details';

const PurePreviewMessage = ({
  chatId,
  messages,
  message,
  vote,
  isLoading,
  setMessages,
  reload,
  isReadonly,
  requiresScrollPadding,
  agent,
  append,
}: {
  chatId: string;
  messages: UIMessage[];
  message: UIMessage;
  vote: Vote | undefined;
  isLoading: boolean;
  setMessages: UseChatHelpers['setMessages'];
  reload: UseChatHelpers['reload'];
  isReadonly: boolean;
  requiresScrollPadding?: boolean;
  agent?: string;
  append: UseChatHelpers['append'];
}) => {
  const [mode, setMode] = useState<'view' | 'edit'>('view');
  const translate_message = useTranslations('message');
  const translate_analitics = useTranslations('analytics_tool');

  const [isSubmitRetryTool, setIsSubmitRetryTool] = useState<boolean>(false);

  return (
    <AnimatePresence>
      <motion.div
        data-testid={`message-${message.role}`}
        className="w-full mx-auto max-w-3xl px-4 group/message"
        initial={{ y: 5, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        data-role={message.role}
      >
        <div
          className={cn(
            'flex gap-4 w-full group-data-[role=user]/message:ml-auto group-data-[role=user]/message:max-w-2xl',
            {
              'w-full': mode === 'edit',
              'group-data-[role=user]/message:w-fit': mode !== 'edit',
            },
          )}
        >
          {message.role === 'assistant' && (
            <div className="size-8 flex items-center rounded-full justify-center ring-1 shrink-0 ring-border bg-background mt-5">
              <div className="translate-y-px">
                <BCA_logo size={40} />
              </div>
            </div>
          )}

          <div
            className={cn('flex flex-col gap-4 w-full max-w-[688px]', {
              'min-h-20': message.role === 'assistant' && requiresScrollPadding,
            })}
          >
            {message.experimental_attachments && (
              <div
                data-testid={`message-attachments`}
                className="flex flex-row justify-end gap-2"
              >
                {message.experimental_attachments.map((attachment) => (
                  <PreviewAttachment
                    key={attachment.url}
                    attachment={attachment}
                  />
                ))}
              </div>
            )}

            {message.parts?.map((part, index) => {
              const { type } = part;
              const key = `message-${message.id}-part-${index}`;

              if (type === 'reasoning') {
                return (
                  <MessageReasoning
                    key={key}
                    isLoading={isLoading}
                    reasoning={part.reasoning}
                  />
                );
              }

              if (type === 'text') {
                if (mode === 'view') {
                  return (
                    <div key={key} className="flex flex-row gap-2 items-start">
                      {message.role === 'user' && !isReadonly && (
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              data-testid="message-edit-button"
                              variant="ghost"
                              className="px-2 h-fit rounded-full text-muted-foreground hidden group-hover/message:block opacity-50 hover:opacity-100 "
                              onClick={() => {
                                setMode('edit');
                              }}
                            >
                              <PencilEditIcon />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            {translate_message('edit_message')}
                          </TooltipContent>
                        </Tooltip>
                      )}

                      <div
                        data-testid="message-content"
                        className={cn('flex flex-col gap-4', {
                          'bg-secondary text-secondary-foreground px-3 py-2 rounded-xl':
                            message.role === 'user',
                        })}
                      >
                        <Markdown>{part.text}</Markdown>
                      </div>
                    </div>
                  );
                }

                if (mode === 'edit') {
                  return (
                    <div key={key} className="flex flex-row gap-2 items-start">
                      <div className="size-8" />

                      <MessageEditor
                        key={message.id}
                        message={message}
                        setMode={setMode}
                        setMessages={setMessages}
                        reload={reload}
                        agent={agent}
                      />
                    </div>
                  );
                }
              }

              if (type === 'tool-invocation') {
                const { toolInvocation } = part;
                const { toolName, toolCallId, state } = toolInvocation;

                if (state === 'call') {
                  const { args } = toolInvocation;

                  return (
                    <div
                      key={toolCallId}
                      className={cx({
                        skeleton: ['getWeather'].includes(toolName),
                      })}
                    >
                      {toolName === 'getWeather' ? (
                        <Weather />
                      ) : toolName === 'createDocument' ? (
                        <DocumentPreview isReadonly={isReadonly} args={args} />
                      ) : toolName === 'updateDocument' ? (
                        <DocumentToolCall
                          type="update"
                          args={args}
                          isReadonly={isReadonly}
                        />
                      ) : toolName === 'requestSuggestions' ? (
                        <DocumentToolCall
                          type="request-suggestions"
                          args={args}
                          isReadonly={isReadonly}
                        />
                      ) : toolName === 'getLeads' ? (
                        <div className="flex flex-row gap-2 items-center justify-start rounded-xl p-4 bg-white">
                          <div className="flex flex-row gap-1 grow-0 animate-spin">
                            <LoaderIcon size={16} />
                          </div>
                          <div className="inline-flex flex-row flex-wrap gap-1 grow-0 basis-full">
                            {translate_message('get_leads_title')}
                            {args.filter && (
                              <>
                                &nbsp;{translate_message('with_filter')}&nbsp;
                                <div className="font-bold">
                                  {(() => {
                                    try {
                                      const filterObj =
                                        typeof args.filter === 'string'
                                          ? JSON.parse(args.filter)
                                          : args.filter;
                                      return Object.entries(filterObj)
                                        .map(
                                          ([key, value]) => `${key}: ${value}`,
                                        )
                                        .join(', ');
                                    } catch (e) {
                                      return args.filter;
                                    }
                                  })()}
                                </div>
                              </>
                            )}
                          </div>
                        </div>
                      ) : toolName === 'createLead' ? (
                        <div className="flex flex-row gap-2 items-center justify-start rounded-xl p-4 bg-white">
                          <div className="flex flex-row gap-1 grow-0 animate-spin">
                            <LoaderIcon size={16} />
                          </div>
                          <div className="inline-flex flex-row flex-wrap gap-1 grow-0 basis-full">
                            {translate_message('create_lead_title')}:
                            <div className="font-bold">
                              {Object.entries(args)
                                .map(([_, value]) => value)
                                .join(', ') || ' - '}
                            </div>
                          </div>
                        </div>
                      ) : toolName === 'performRagQuery' ? (
                        <div className="flex flex-row gap-2 items-center justify-start text-green-500 rounded-xl p-4 bg-white ">
                          <div className="flex flex-row gap-1 grow-0 animate-spin">
                            <LoaderIcon size={16} />
                          </div>
                          <div className="inline-flex flex-row flex-wrap gap-1 grow-0 basis-full">
                            {translate_message('call_perform_rag_query')}:
                            <div className="font-bold">
                              &apos;{args.query}&lsquo;
                            </div>
                            {translate_message('call_perform_rag_query_source')}
                            <div className="font-bold">
                              &apos;{args.source}&lsquo;
                            </div>
                          </div>
                        </div>
                      ) : toolName === 'getChatActivityMetrics' ? (
                        <div className="flex flex-row gap-2 items-center justify-start text-blue-500 rounded-xl p-4 bg-white ">
                          <div className="flex flex-row gap-1 grow-0 animate-spin">
                            <LoaderIcon size={16} />
                          </div>
                          <div className="inline-flex flex-row flex-wrap gap-1 grow-0 basis-full">
                            <div className="font-bold">
                              &apos;{translate_analitics('processing')}&lsquo;
                            </div>
                          </div>
                        </div>
                      ) : toolName === 'getPotentialLeads' ? (
                        <div className="flex flex-row gap-2 items-center justify-start rounded-xl p-4 bg-white">
                          <div className="flex flex-row gap-1 grow-0 animate-spin">
                            <LoaderIcon size={16} />
                          </div>
                          <div className="inline-flex flex-row flex-wrap gap-1 grow-0 basis-full">
                            {translate_message('get_potential_leads_title')}
                            {args.filters && (
                              <>
                                &nbsp;{translate_message('with_filter')}&nbsp;
                                <div className="font-bold">
                                  {(() => {
                                    try {
                                      const filterObj =
                                        typeof args.filters === 'string'
                                          ? JSON.parse(args.filters)
                                          : args.filters;
                                      return Object.entries(filterObj)
                                        .map(
                                          ([key, value]) => `${key}: ${value}`,
                                        )
                                        .join(', ');
                                    } catch (e) {
                                      return args.filters;
                                    }
                                  })()}
                                </div>
                              </>
                            )}
                          </div>
                        </div>
                      ) : toolName === 'getPotentialLeadDetails' ? (
                        <div className="flex flex-row gap-2 items-center justify-start rounded-xl p-4 bg-white">
                          <div className="flex flex-row gap-1 grow-0 animate-spin">
                            <LoaderIcon size={16} />
                          </div>
                          <div className="inline-flex flex-row flex-wrap gap-1 grow-0 basis-full">
                            Получение деталей потенциального лида...
                            {(args.orderId || args.name || args.lastName || args.email || args.phone) && (
                              <>
                                &nbsp;по параметрам:&nbsp;
                                <div className="font-bold">
                                  {[
                                    args.orderId && `ID: ${args.orderId}`,
                                    args.name && `Имя: ${args.name}`,
                                    args.lastName && `Фамилия: ${args.lastName}`,
                                    args.email && `Email: ${args.email}`,
                                    args.phone && `Телефон: ${args.phone}`
                                  ].filter(Boolean).join(', ')}
                                </div>
                              </>
                            )}
                          </div>
                        </div>
                      ) : null}
                    </div>
                  );
                }

                if (state === 'result') {
                  const { result } = toolInvocation;
                  return (
                    <div key={toolCallId}>
                      {toolName === 'getWeather' ? (
                        <Weather weatherAtLocation={result} />
                      ) : toolName === 'createDocument' ? (
                        <DocumentPreview
                          isReadonly={isReadonly}
                          result={result}
                        />
                      ) : toolName === 'updateDocument' ? (
                        <DocumentToolResult
                          type="update"
                          result={result}
                          isReadonly={isReadonly}
                        />
                      ) : toolName === 'requestSuggestions' ? (
                        <DocumentToolResult
                          type="request-suggestions"
                          result={result}
                          isReadonly={isReadonly}
                        />
                      ) : toolName === 'getLeads' ? (
                        <>
                          {result.data && result.success ? (
                            <LeadsDataTable leads={result.data} />
                          ) : (
                            <ErrorMessageWithRetry
                              errorMessage={translate_message(
                                'error_get_leads',
                              )}
                              isSubmitRetryTool={isSubmitRetryTool}
                              setIsSubmitRetryTool={setIsSubmitRetryTool}
                              message={message}
                              messages={messages}
                              setMessages={setMessages}
                              reload={reload}
                            />
                          )}
                        </>
                      ) : toolName === 'createLead' ? (
                        <>
                          {result.data && result.success ? (
                            <div className="flex flex-col gap-2 rounded-xl p-4 bg-white">
                              <div className="flex flex-row gap-2 items-center justify-start text-green-500  mb-2">
                                <div className="flex flex-row gap-2 grow-0">
                                  <InfoIcon size={16} />
                                </div>
                                <div className="flex flex-col gap-1 grow-0 basis-full">
                                  <div className="font-medium">
                                    {translate_message('lead_created_success')}
                                  </div>
                                </div>
                              </div>
                              <LeadsDataTable
                                leads={[result.data]}
                                showAmount={false}
                                showExpandButton={false}
                              />
                              {/* Send GA4 event when a lead is successfully created */}
                              {typeof window !== 'undefined' && 
                                // @ts-ignore
                                window.gtag && 
                                // @ts-ignore
                                window.gtag('event', 'create_lead', {
                                  event_category: 'Lead',
                                  event_label: 'New Lead Created',
                                  value: result.data.ID,
                                })
                              }
                            </div>
                          ) : (
                            <ErrorMessageWithRetry
                              errorMessage={translate_message(
                                'error_create_lead',
                              )}
                              isSubmitRetryTool={isSubmitRetryTool}
                              setIsSubmitRetryTool={setIsSubmitRetryTool}
                              message={message}
                              messages={messages}
                              setMessages={setMessages}
                              reload={reload}
                            />
                          )}
                        </>
                      ) : toolName === 'performRagQuery' ? (
                        <>
                          {result.data && result.success ? (
                            <div className="flex flex-row gap-2 items-center justify-start text-green-500 rounded-xl p-4 bg-white ">
                              <div className="flex flex-row gap-2 grow-0 ">
                                <InfoIcon size={16} />
                              </div>
                              <div className="flex flex-row gap-1 grow-0 basis-full">
                                {translate_message('result_perform_rag_query')}
                                <div className="font-bold">
                                  {result.data.length}
                                </div>
                                {translate_message(
                                  'result_perform_rag_query_count',
                                )}
                              </div>
                            </div>
                          ) : (
                            <ErrorMessageWithRetry
                              errorMessage={translate_message(
                                'error_perform_rag_query',
                              )}
                              isSubmitRetryTool={isSubmitRetryTool}
                              setIsSubmitRetryTool={setIsSubmitRetryTool}
                              message={message}
                              messages={messages}
                              setMessages={setMessages}
                              reload={reload}
                            />
                          )}
                        </>
                      ) : toolName === 'getChatActivityMetrics' ? (
                        <>
                          {result.success ? (
                            <div className="flex flex-row gap-2 items-center justify-start text-green-500 rounded-xl p-4 bg-white">
                              <div className="flex flex-row gap-2 grow-0">
                                <InfoIcon size={16} />
                              </div>
                              <div className="inline-flex flex-row flex-wrap gap-1 grow-0 basis-full">
                                <div className="font-bold">
                                  {translate_analitics('finished')}
                                </div>
                              </div>
                            </div>
                          ) : (
                            <ErrorMessageWithRetry
                              errorMessage={translate_analitics('error')}
                              isSubmitRetryTool={isSubmitRetryTool}
                              setIsSubmitRetryTool={setIsSubmitRetryTool}
                              message={message}
                              messages={messages}
                              setMessages={setMessages}
                              reload={reload}
                            />
                          )}
                        </>
                      ) : toolName === 'getPotentialLeads' ? (
                        <>
                          {result.data && result.success ? (
                            <PotentialLeadsTable
                              key={message.id}
                              initialLeads={result.data}
                              initialCountAll={result.count_all}
                              initialPagination={result.pagination}
                              initialFilters={result.filters}
                              initialSortBy={result.sortBy}
                              initialSortOrder={result.sortOrder}
                              append={append}
                            />
                          ) : (
                            <ErrorMessageWithRetry
                              errorMessage={translate_message(
                                'error_get_potential_leads',
                              )}
                              isSubmitRetryTool={isSubmitRetryTool}
                              setIsSubmitRetryTool={setIsSubmitRetryTool}
                              message={message}
                              messages={messages}
                              setMessages={setMessages}
                              reload={reload}
                            />
                          )}
                        </>
                      ) : toolName === 'getPotentialLeadDetails' ? (
                        <>
                          {result.data && result.success ? (
                            <PotentialLeadDetails
                              key={message.id}
                              data={result.data}
                            />
                          ) : (
                            <ErrorMessageWithRetry
                              errorMessage={result.error || 'Ошибка при получении деталей потенциального лида'}
                              isSubmitRetryTool={isSubmitRetryTool}
                              setIsSubmitRetryTool={setIsSubmitRetryTool}
                              message={message}
                              messages={messages}
                              setMessages={setMessages}
                              reload={reload}
                            />
                          )}
                        </>
                      ) : (
                        <pre>{JSON.stringify(result, null, 2)}</pre>
                      )}
                    </div>
                  );
                }
              }
            })}

            {!isReadonly && (
              <MessageActions
                key={`action-${message.id}`}
                chatId={chatId}
                message={message}
                vote={vote}
                isLoading={isLoading}
                //@TODO add agent model name
              />
            )}
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
};

export const PreviewMessage = memo(
  PurePreviewMessage,
  (prevProps, nextProps) => {
    if (prevProps.isLoading !== nextProps.isLoading) return false;
    if (prevProps.message.id !== nextProps.message.id) return false;
    if (prevProps.requiresScrollPadding !== nextProps.requiresScrollPadding)
      return false;
    if (!equal(prevProps.message.parts, nextProps.message.parts)) return false;
    if (!equal(prevProps.vote, nextProps.vote)) return false;

    return true;
  },
);

export const ThinkingMessage = () => {
  const role = 'assistant';
  const translation_message = useTranslations('message');

  return (
    <motion.div
      data-testid="message-assistant-loading"
      className="w-full mx-auto max-w-3xl px-4 group/message min-h-20"
      initial={{ y: 5, opacity: 0 }}
      animate={{ y: 0, opacity: 1, transition: { delay: 1 } }}
      data-role={role}
    >
      <div
        className={cx(
          'flex gap-4 group-data-[role=user]/message:px-3 w-full group-data-[role=user]/message:w-fit group-data-[role=user]/message:ml-auto group-data-[role=user]/message:max-w-2xl group-data-[role=user]/message:py-2 rounded-xl',
          {
            'group-data-[role=user]/message:bg-muted': true,
          },
        )}
      >
        <div className="size-8 flex items-center rounded-full justify-center ring-1 shrink-0 ring-border">
          <SparklesIcon size={14} />
        </div>

        <div className="flex flex-col gap-2 w-full">
          <div className="flex flex-col gap-4 text-muted-foreground">
            {translation_message('assistant_message')}
          </div>
        </div>
      </div>
    </motion.div>
  );
};
