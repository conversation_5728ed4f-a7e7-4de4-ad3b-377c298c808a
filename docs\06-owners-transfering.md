### Документация по передаче проекта

1. **Создание репозитория на GitHub и загрузка файлов проекта**
    - Перейдите на [GitHub](https://github.com) и создайте новый репозиторий.
    - При необходимости инициализируйте репозиторий с README.
    - Загрузите локальные файлы проекта в этот репозиторий:
      ```bash
      git remote add origin <repository-url>
      git branch -M main
      git push -u origin main
      ```


2. **Перенастройка Vercel для нового проекта GitHub**
    - Войдите в свой аккаунт [Vercel](https://vercel.com).
    - Удалите существующую конфигурацию проекта, связанную со старым репозиторием.
    - Импортируйте новый репозиторий GitHub в Vercel.

3. **Проверка деплоя из ветки `main`**
    - Убедитесь, что Vercel настроен на автоматический деплой из ветки `main`.
    - При необходимости запустите ручной деплой и проверьте успешность развертывания приложения:
        - Перейдите во вкладку "Deployments" (Деплои) в Vercel.
        - Проверьте логи на наличие ошибок или проблем во время сборки.

4. **Вход в настройки проекта на GitHub**
    - Откройте свой проект на GitHub.
    - Нажмите на вкладку "Settings" (Настройки) в правом верхнем углу страницы репозитория.

5. **Инициирование передачи прав собственности**
    - Прокрутите до раздела "Danger Zone" (Опасная зона) в настройках.
    - Нажмите кнопку "Transfer ownership" (Передать права собственности).
    - Введите имя пользователя или организации, которой вы хотите передать репозиторий.
    - Подтвердите передачу, введя имя репозитория, когда будет предложено.

6. **Вход в проект Vercel**
    - В панели управления Vercel найдите проект, связанный с репозиторием.
    - Откройте настройки проекта, нажав на значок шестеренки рядом с названием проекта.

7. **Передача прав собственности в Vercel**
    - В настройках проекта перейдите в раздел "Owners transferring" (Передача владельцев).
    - Следуйте инструкциям, чтобы передать права собственности на проект Vercel в соответствии с новым владельцем репозитория GitHub.

---

### Дополнительные замечания
- **Предварительные требования**: Убедитесь, что у вас есть права администратора как на GitHub, так и на Vercel перед началом процесса.
- **Тестирование**: После передачи прав тщательно протестируйте проект в новой среде, чтобы убедиться, что все работает корректно.
- **Коммуникация**: Сообщите всем заинтересованным сторонам о смене владельца, чтобы избежать путаницы.

---
