{"name": "ai-chatbot", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "tsx lib/db/migrate && next build", "build:without_migrations": "next build", "start": "next start", "lint": "next lint && biome lint --write --unsafe", "lint:fix": "next lint --fix && biome lint --write --unsafe", "format": "biome format --write", "db:generate": "drizzle-kit generate", "db:migrate": "npx tsx lib/db/migrate.ts", "db:studio": "drizzle-kit studio", "db:push": "drizzle-kit push", "db:pull": "drizzle-kit pull", "db:check": "drizzle-kit check", "db:up": "drizzle-kit up", "test": "export PLAYWRIGHT=True && pnpm exec playwright test --workers=4", "test-search-vector": "tsx lib/rag/pgvec.search.test.ts"}, "dependencies": {"@ai-sdk/fireworks": "^0.2.11", "@ai-sdk/groq": "^1.2.8", "@ai-sdk/openai": "^1.3.15", "@ai-sdk/react": "^1.2.9", "@ai-sdk/ui-utils": "^1.2.8", "@ai-sdk/xai": "^1.2.13", "@auth/drizzle-adapter": "^1.9.1", "@codemirror/lang-javascript": "^6.2.3", "@codemirror/lang-python": "^6.1.7", "@codemirror/state": "^6.5.2", "@codemirror/theme-one-dark": "^6.1.2", "@codemirror/view": "^6.36.5", "@google-analytics/data": "^5.1.0", "@modelcontextprotocol/sdk": "^1.11.0", "@next/third-parties": "^15.3.3", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tooltip": "^1.2.4", "@radix-ui/react-visually-hidden": "^1.1.3", "@types/chai": "^5.2.1", "@types/mocha": "^10.0.10", "@vercel/analytics": "^1.5.0", "@vercel/blob": "^0.24.1", "@vercel/postgres": "^0.10.0", "@xenova/transformers": "^2.17.2", "ai": "4.2.0", "bcrypt-ts": "^5.0.3", "chai": "^5.2.0", "class-variance-authority": "^0.7.1", "classnames": "^2.5.1", "clsx": "^2.1.1", "codemirror": "^6.0.1", "crypto": "^1.0.1", "date-fns": "^4.1.0", "diff-match-patch": "^1.0.5", "dotenv": "^16.5.0", "drizzle-orm": "^0.34.1", "fast-deep-equal": "^3.1.3", "framer-motion": "^11.18.2", "fs": "0.0.1-security", "geist": "^1.3.1", "i18next": "^25.0.0", "i18next-http-backend": "^3.0.2", "jose": "^6.0.10", "js-cookies": "^1.0.4", "lucide-react": "^0.446.0", "nanoid": "^5.1.5", "next": "15.3.0-canary.12", "next-auth": "5.0.0-beta.25", "next-client-cookies": "^2.0.1", "next-cookies": "^2.0.3", "next-i18next": "^15.4.2", "next-intl": "^4.0.2", "next-themes": "^0.3.0", "nodemailer": "^7.0.3", "orderedmap": "^2.1.1", "papaparse": "^5.5.2", "path": "^0.12.7", "postgres": "^3.4.5", "prosemirror-example-setup": "^1.2.3", "prosemirror-inputrules": "^1.5.0", "prosemirror-markdown": "^1.13.2", "prosemirror-model": "^1.25.0", "prosemirror-schema-basic": "^1.2.4", "prosemirror-schema-list": "^1.5.1", "prosemirror-state": "^1.4.3", "prosemirror-view": "^1.39.1", "react": "19.0.0-rc-45804af1-20241021", "react-data-grid": "7.0.0-beta.47", "react-dom": "19.0.0-rc-45804af1-20241021", "react-i18next": "^15.4.1", "react-markdown": "^9.1.0", "react-resizable-panels": "^2.1.7", "remark-gfm": "^4.0.1", "server-only": "^0.0.1", "sonner": "^1.7.4", "swr": "^2.3.3", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "usehooks-ts": "^3.1.1", "zod": "^3.24.3"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@playwright/test": "^1.51.1", "@tailwindcss/typography": "^0.5.16", "@tsconfig/node18": "^18.2.4", "@types/d3-scale": "^4.0.9", "@types/node": "^22.14.1", "@types/papaparse": "^5.3.15", "@types/pdf-parse": "^1.1.5", "@types/react": "^18.3.20", "@types/react-dom": "^18.3.6", "drizzle-kit": "^0.25.0", "eslint": "^8.57.1", "eslint-config-next": "14.2.5", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.10.0", "eslint-plugin-tailwindcss": "^3.18.0", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "tsx": "^4.19.3", "typescript": "^5.8.3"}, "packageManager": "pnpm@9.12.3"}