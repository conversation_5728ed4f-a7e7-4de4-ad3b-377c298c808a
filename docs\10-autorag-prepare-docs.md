Вот структурированный документ в формате Markdown, содержащий актуальные ограничения и инструкцию по подготовке документов для использования с Cloudflare AutoRAG:

---

# Руководство по подготовке документов для Cloudflare AutoRAG

## Ограничения AutoRAG

### 1. Размер файлов

- **Текстовые файлы** (например, `.txt`, `.md`, `.json`): максимальный размер — **4 МБ**.
- **Файлы в богатом формате** (например, `.pdf`, `.docx`, `.xlsx`): максимальный размер — **1 МБ**.

Файлы, превышающие эти ограничения, не будут проиндексированы, и информация о них появится в логах ошибок. [Подробнее](https://developers.cloudflare.com/autorag/platform/limits-pricing/)

### 2. Поддерживаемые форматы файлов

#### Текстовые форматы:

- `.txt`, `.md`, `.json`, `.yaml`, `.ini`, `.py`, `.js`, `.java`, `.c`, `.cpp`, и другие.

#### Расширенные форматы:

- `.pdf`, `.docx`, `.xlsx`, `.csv`, `.html`, `.xml`, `.svg`, `.png`, `.jpg`, `.webp`, `.numbers`, и другие.

Эти файлы автоматически конвертируются в Markdown перед индексацией. [Подробнее](https://developers.cloudflare.com/autorag/platform/limits-pricing/)

### 3. Лимиты по количеству

- До 10 AutoRAG-инстанций на аккаунт.
- До 100 000 файлов на одну инстанцию. [Подробнее](https://developers.cloudflare.com/autorag/platform/limits-pricing/)

## Инструкция по подготовке документов

### Шаг 1: Подготовка файлов

- Для текстовых файлов: убедитесь, что они имеют поддерживаемые расширения и не превышают 4 МБ.
- Для файлов в богатом формате: проверьте, что их размер не превышает 1 МБ. Если возможно, предварительно конвертируйте их в Markdown для лучшей обработки.

### Шаг 2: Загрузка в Cloudflare R2

1. Войдите в [Cloudflare Dashboard](https://dash.cloudflare.com/).
2. Перейдите в раздел **R2** и создайте новый бакет.
3. Загрузите подготовленные файлы в созданный бакет.

### Шаг 3: Создание AutoRAG-инстанции

1. В Dashboard перейдите в раздел **AI > AutoRAG**.
2. Нажмите **Create AutoRAG** и следуйте инструкциям для настройки, указав созданный R2-бакет в качестве источника данных.
3. После создания начнётся автоматическая индексация загруженных файлов.

### Шаг 4: Мониторинг и тестирование

- Статус индексации можно отслеживать на странице инстанции AutoRAG в разделе **Overview**.
- Для тестирования используйте вкладку **Playground**, где можно вводить запросы и получать ответы на основе загруженных данных. [Подробнее](https://developers.cloudflare.com/autorag/get-started/)

## Рекомендации

- **Структурируйте контент**: используйте заголовки и логическую структуру в документах для улучшения качества ответов.
- **Начинайте с малого**: тестируйте работу AutoRAG на небольшом наборе документов перед масштабированием.
- **Используйте AI Gateway**: для мониторинга использования и управления затратами.

---

Если у вас возникнут дополнительные вопросы или потребуется помощь с конкретными аспектами интеграции, пожалуйста, дайте знать! 
