'use client';

import React from 'react';
import { useFormStatus } from 'react-dom';

import { LoaderIcon } from '@/components/icons';

import { Button } from './ui/button';

export function SubmitButton({
  children,
  isSuccessful,
  disabled = false,
}: {
  children: React.ReactNode;
  isSuccessful: boolean;
  disabled?: boolean;
}) {
  const { pending } = useFormStatus();

  return (
    <Button
      type={pending ? 'button' : 'submit'}
      variant={'secondary'}
      aria-disabled={pending || isSuccessful || disabled}
      disabled={pending || isSuccessful || disabled}
      className="relative bg-sidebar-primary hover:bg-white hover:text-sidebar-primary"
    >
      {children}

      {(pending || isSuccessful) && (
        <span className="animate-spin absolute right-4">
          <LoaderIcon />
        </span>
      )}

      <output aria-live="polite" className="sr-only">
        {pending || isSuccessful ? 'Загрузка' : 'Отправить'}
      </output>
    </Button>
  );
}
