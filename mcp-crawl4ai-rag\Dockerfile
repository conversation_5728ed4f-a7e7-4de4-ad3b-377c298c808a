FROM python:3.12-slim

ARG PORT=8051

WORKDIR /app

# Install system dependencies for PostgreSQL and other libraries
RUN apt-get update && apt-get install -y \
    libpq-dev \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Install uv
RUN pip install uv

# Copy the MCP server files
COPY . .

# Install packages directly to the system (no virtual environment)
# Combining commands to reduce Docker layers
RUN uv pip install --system -e . && \
    uv pip install --system psycopg2-binary numpy && \
    crawl4ai-setup

EXPOSE ${PORT}

# Command to run the MCP server
CMD ["uv", "run", "src/crawl4ai_mcp.py"]