'use client';

import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { useActionState, useEffect, useState, Suspense } from 'react';
import { toast } from '@/components/toast';

import { AuthForm } from '@/components/auth-form';
import { SubmitButton } from '@/components/submit-button';

import { login, type LoginActionState } from '../actions';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import HorizontalLanguageSwitcher from '@/components/hor-language-switcher';
import { GoogleSignInButton } from '@/components/google-signin-button';
import { getAuthErrorMessageByCode } from '@/lib/utils';
import { ResendAuthForm } from '@/components/resend-signin-form';
import { AppleSignInButton } from '@/components/apple-signin-button';
import { MagicLinkSignInButton } from '@/components/magic-link-signin-button';
import { motion } from 'framer-motion';
import { ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';

function RegisterLink() {
  const searchParams = useSearchParams();
  const callbackUrl = searchParams.get('callbackUrl');
  const translation_login_page = useTranslations('login_page');
  return (
    <>
      {callbackUrl ? (
        <Link
          href={`/register?callbackUrl=${callbackUrl}`}
          className="font-semibold text-secondary hover:underline"
        >
          {translation_login_page('register_link')}
        </Link>
      ) : (
        <Link
          href="/register"
          className="font-semibold text-secondary hover:underline"
        >
          {translation_login_page('register_link')}
        </Link>
      )}
    </>
  );
}

export default function Page() {
  const router = useRouter();

  const [email, setEmail] = useState('');
  const [isSuccessful, setIsSuccessful] = useState(false);
  const translation_login_page = useTranslations('login_page');
  const searchParams = useSearchParams();
  const errorCode = searchParams.get('error');
  const errorMessage = getAuthErrorMessageByCode(errorCode);

  const [state, formAction] = useActionState<LoginActionState, FormData>(
    login,
    {
      status: 'idle',
    },
  );

  useEffect(() => {
    if (state.status === 'failed') {
      toast({
        type: 'error',
        description: translation_login_page('error_invalid_credentials'),
      });
    } else if (state.status === 'invalid_data') {
      toast({
        type: 'error',
        description: translation_login_page('error_invalid_data'),
      });
    } else if (errorCode) {
      toast({
        type: 'error',
        description: errorMessage?.description,
      });
    } else if (state.status === 'success') {
      setIsSuccessful(true);
      router.refresh();
    }
  }, [state.status, router]);

  const handleSubmit = (formData: FormData) => {
    setEmail(formData.get('email') as string);
    formAction(formData);
  };

  const [offerAccepted, setOfferAccepted] = useState(false);

  const handleOfferChange = (value: boolean) => {
    setOfferAccepted(value);
  };

  const [isMagicLinkFormVisible, setIsFormVisible] = useState(false);

  const toggleMagicLinkForm = () => {
    setIsFormVisible(!isMagicLinkFormVisible);
    setOfferAccepted(false);
  };

  return (
    <div
      className="flex flex-col md:flex-row min-h-dvh overflow-y-auto w-screen items-start pt-0 md:items-center justify-center bg-primary bg-[url('/images/Background.png')] bg-no-repeat bg-cover bg-center"
      style={{
        backgroundSize: '110% 98%',
      }}
    >
      {/* Языковой переключатель */}
      <div className="absolute top-4 right-4 z-10">
        <HorizontalLanguageSwitcher />
      </div>
      <div className="flex items-center jYustify-between self-center">
        <div className="flex flex-col items-center gap-2 text-white max-w-[300px]">
          <Image
            src="/images/Icon_user_login.png"
            alt="Agent image"
            width={300}
            height={48}
            className="flex flex-row items-center gap-2 ml-[50px] mr-[50px] max-w-[150px] md:max-w-[300px]"
            priority
          />
          <span className="flex flex-col italic text-sm md:text-xl">
            {translation_login_page('intro_message')}
          </span>
          <span className="flex flex-col font-bold italic text-xs md:text-md self-end pt-4">
            {translation_login_page('intro_author')}
          </span>
        </div>
      </div>
      <div className="w-full max-w-md overflow-hidden rounded-2xl flex flex-col gap-2 md:gap-12 pt-6 md:pt-0 self-center">
        <div className="flex flex-col items-center justify-center gap-2 px-4 text-center sm:px-16 text-white">
          <h3 className="text-md md:text-xl font-semibold ">
            {translation_login_page('login_title')}
          </h3>
          {isMagicLinkFormVisible ? (
            <p className="text-xs md:text-sm">
              {translation_login_page('magick_link_subtitle')}
            </p>
          ) : (
            <p className="text-xs md:text-sm">
              {translation_login_page('login_subtitle')}
            </p>
          )}
        </div>
        {isMagicLinkFormVisible ? (
          <motion.div
            key="magic-link-form"
            className="bg-transparent"
            initial={{ opacity: 0, scale: 0 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0, transition: { delay: 0.4 } }}
          >
            <ResendAuthForm
              defaultEmail={email}
              offerAccepted={offerAccepted}
              setOfferAccepted={handleOfferChange}
              toggleMagicLinkForm={toggleMagicLinkForm}
            />
          </motion.div>
        ) : (
          <motion.div
            className="bg-transparent"
            key="auth-form"
            initial={{ opacity: 0, scale: 0 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0, transition: { delay: 0.4 } }}
          >
            <AuthForm
              action={handleSubmit}
              defaultEmail={email}
              offerAccepted={offerAccepted}
              setOfferAccepted={handleOfferChange}
              toggleMagicLinkForm={toggleMagicLinkForm}
            >
              <SubmitButton
                isSuccessful={isSuccessful}
                disabled={!offerAccepted}
              >
                {translation_login_page('login_button')}
              </SubmitButton>
              <p className="text-center text-xs text-gray-300 -mt-2">
                {translation_login_page('no_account')}

                <Suspense fallback={null}>
                  <RegisterLink />
                </Suspense>
                {translation_login_page('register_suffix')}
              </p>

              <p className="text-center text-base text-white my-4">
                {translation_login_page('login_another_way')}
              </p>

              <p className="flex flex-row justify-center gap-6 text-center text-xs md:text-sm text-gray-300">
                <Suspense fallback={null}>
                  <GoogleSignInButton />
                  <AppleSignInButton />
                  <MagicLinkSignInButton toggleForm={toggleMagicLinkForm} />
                </Suspense>
              </p>
              <Button
                variant="link"
                type="button"
                size="sm"
                className="text-white !hover:text-black p-8 "
                onClick={() => router.push('/')}
              >
                <ArrowLeft className="flex !w-[30px] !h-[30px] text-white !hover:text-black" />{' '}
                {translation_login_page('back')}
              </Button>
            </AuthForm>
          </motion.div>
        )}
      </div>
    </div>
  );
}
