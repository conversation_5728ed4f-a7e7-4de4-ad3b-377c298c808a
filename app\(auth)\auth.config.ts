import type { NextAuthConfig } from 'next-auth';
import { getToken } from 'next-auth/jwt';

export enum UserRole {
  USER = 'user',
  ADMIN = 'admin',
  MODERATOR = 'moderator',
}

export const authConfig = {
  pages: {
    signIn: '/login',
    newUser: '/',
    error: '/error',
  },
  providers: [
    // added later in auth.ts since it requires bcrypt which is only compatible with Node.js
    // while this file is also used in non-Node.js environments
  ],
  useSecureCookies: process.env.NODE_ENV === 'production',
  secret: process.env.NEXTAUTH_SECRET || process.env.AUTH_SECRET,
  callbacks: {
    async authorized({ auth, request }) {
      const { nextUrl } = request;
      console.log('[Auth] Authorizing request to:', nextUrl.pathname);
      const isLoggedIn = !!auth?.user;
      const isOnMainPage = nextUrl.pathname === '/';
      const isOnAgentChat = nextUrl.pathname.startsWith('/chat');
      const isOnRegister = nextUrl.pathname.startsWith('/register');
      const isOnLogin = nextUrl.pathname.startsWith('/login');
      const isOnError = nextUrl.pathname.startsWith('/error');
      const isOnSent = nextUrl.pathname.startsWith('/sent');

      const token = await getToken({
        req: request,
        secret: process.env.NEXTAUTH_SECRET || process.env.AUTH_SECRET,
        secureCookie: process.env.NODE_ENV === 'production',
      });

      const callbackUrl = nextUrl.searchParams.get('callbackUrl');

      if (isLoggedIn && (isOnLogin || isOnRegister || isOnMainPage)) {
        if (token?.role === UserRole.MODERATOR) {
          return Response.redirect(new URL('/chat/moderator', nextUrl));
        }

        if (callbackUrl) {
          return Response.redirect(callbackUrl);
        }

        if (isOnMainPage) {
          return true;
        }

        return Response.redirect(new URL('/', nextUrl));
      }

      if (isOnRegister || isOnLogin || isOnError || isOnSent || isOnMainPage) {
        return true; // Always allow access to register, login, error pages
      }

      if (isOnAgentChat) {
        if (isLoggedIn) return true;
        return false; // Redirect unauthenticated users to login page
      }

      return isLoggedIn;
    },
  },
} satisfies NextAuthConfig;
