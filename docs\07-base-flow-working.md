# Бизнес-процесс работы ИИ-агента

## Описание процесса

Этот процесс описывает взаимодействие между клиентом, ИИ-агентом, клиентской и нормативной базами данных, а также контекстом взаимодействий. Основной целью является эффективное решение проблемы клиента с использованием данных из клиентской и нормативной базы, а также контекста прошлых взаимодействий.

### Участники процесса

- **Клиент** — конечный пользователь, инициирующий обращение в систему.
- **ИИ_Агент** — искусственный интеллект, который обрабатывает запрос клиента, используя данные из различных источников.
- **Клиентская_База** — база данных, содержащая информацию о клиентах, их обращениях и историях.
- **Нормативная_База** — база данных с нормативными актами и юридическими нормами, необходимыми для решения запросов.
- **Контекст** — система, которая хранит историю взаимодействий и может предоставить информацию о предыдущих обращениях клиента.

### Шаги процесса

1. **Инициация обращения клиента**
    - Клиент инициирует обращение в систему через ИИ-агента. Этот запрос может быть получен через различные каналы (например, веб-формы, чат-боты и т.д.).

2. **Проверка существования клиента**
    - ИИ-агент отправляет запрос в клиентскую базу данных для проверки существования клиента.
    - Если данные клиента есть в базе, ИИ-агент получает информацию, связанную с клиентом.

3. **Запрос деталей проблемы**
    - ИИ-агент запрашивает у клиента подробности о возникшей проблеме. Эти данные будут использованы для дальнейшей обработки.

4. **Предоставление деталей проблемы клиентом**
    - Клиент предоставляет ИИ-агенту необходимые данные о проблеме (например, описание ситуации, предыдущие обращения и т.д.).

5. **Запись или обновление информации в клиентской базе**
    - ИИ-агент передает обновленные данные в клиентскую базу для записи или модификации существующей информации.

6. **Проверка истории взаимодействий**
    - ИИ-агент обращается к системе контекста для получения информации о предыдущих взаимодействиях с этим клиентом.
    - Если такая информация существует, она передается ИИ-агенту.

7. **Поиск нормативных актов**
    - ИИ-агент обращается к нормативной базе данных для поиска актуальных юридических норм, которые могут быть полезны для решения проблемы клиента.

8. **Предоставление решения клиенту**
    - На основании собранной информации ИИ-агент формулирует и передает решение проблемы клиенту.

9. **Подтверждение получения решения**
    - Клиент подтверждает получение решения. В случае удовлетворенности клиент может закрыть обращение.

10. **Запись завершения взаимодействия**
    - ИИ-агент записывает факт завершения обращения в клиентскую базу данных, чтобы сохранить историю и обработку запроса.

## Интеграции и зависимости

1. **Клиентская база**: необходима для получения и обновления данных клиента.
    - База должна поддерживать функции добавления, изменения и поиска информации о клиентах.

2. **Нормативная база**: требуется для поиска актуальных нормативных актов.
    - База должна поддерживать быстрый поиск по юридическим нормам, и она должна быть регулярно обновляемой.

3. **Контекст**: служит для получения данных о предыдущих взаимодействиях.
    - Контекст должен быть способен хранить и извлекать историю взаимодействий на основе уникальных идентификаторов клиентов.
    
## Пример работы

### Пример 1: Инициация запроса клиента

1. Клиент начинает чат с ИИ-агентом.
2. ИИ-агент проверяет в клиентской базе, есть ли этот клиент.
    - Если есть, извлекаются его данные (имя, контакты, предыдущие обращения).
3. ИИ-агент запрашивает у клиента подробности по текущей проблеме.
4. Клиент отправляет описание проблемы.
5. ИИ-агент записывает или обновляет информацию в клиентской базе.
6. ИИ-агент проверяет историю взаимодействий с клиентом через контекст.
7. ИИ-агент находит релевантные юридические нормы в нормативной базе.
8. ИИ-агент формулирует и передает решение клиенту.

### Пример 2: Завершение запроса

1. Клиент подтверждает получение решения.
2. ИИ-агент завершает взаимодействие и записывает это в клиентскую базу.

##Диаграмма последовательности
```Mermaid
sequenceDiagram
    participant Клиент
    participant ИИ_Агент
    participant Клиентская_База
    participant Нормативная_База
    participant Контекст
    
    Клиент->>ИИ_Агент: Инициация обращения
    ИИ_Агент->>Клиентская_База: Проверка существования клиента
    Клиентская_База-->>ИИ_Агент: Данные клиента (если есть)
    
    ИИ_Агент->>Клиент: Запрос деталей проблемы
    Клиент->>ИИ_Агент: Предоставление деталей
    ИИ_Агент->>Клиентская_База: Запись или обновление информации

    ИИ_Агент->>Контекст: Проверка истории взаимодействий
    Контекст-->>ИИ_Агент: Данные прошлых обращений (если есть)
    
    ИИ_Агент->>Нормативная_База: Поиск юридических норм
    Нормативная_База-->>ИИ_Агент: Актуальные нормативы
      
    ИИ_Агент->>Клиент: Предоставление решения
    Клиент->>ИИ_Агент: Подтверждение получения
    ИИ_Агент->>Клиентская_База: Запись завершения взаимодействия
```

###rem:
Для просмотра диаграммы использовать [Mermaid Live Editor](https://mermaid.live/edit) 
