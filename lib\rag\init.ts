import { pgvectorProcessdoc, uploadDocuments } from '@/lib/rag/vectorDB/operations.pgvec';
import path from 'path';

const SOURCE_DIR = path.join(__dirname, 'source_documents');

export async function initializeRAGDocuments() {
    try {
        // Step 1: Upload documents from source directory
        console.log('Starting document upload...');
        uploadDocuments(SOURCE_DIR);

        // Step 2: Process uploaded documents
        console.log('Processing documents...');
        await pgvectorProcessdoc();

        console.log('RAG document initialization completed successfully.');
    } catch (error) {
        console.error('Error during RAG initialization:', error);
    }
}

initializeRAGDocuments().then(() => {
    console.log('RAG Finished.');
    process.exit(0);
});
