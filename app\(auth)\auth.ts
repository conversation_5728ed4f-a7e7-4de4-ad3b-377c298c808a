import { compare } from 'bcrypt-ts';
import NextAuth, { type User, type Session } from 'next-auth';
import Credentials from 'next-auth/providers/credentials';
import GoogleProvider from 'next-auth/providers/google';
import AppleProvider from 'next-auth/providers/apple';
import ResendProvider from 'next-auth/providers/resend';
import { DrizzleAdapter } from '@auth/drizzle-adapter';

import { createUser, getUser, updateUser } from '@/lib/db/queries';

import { authConfig } from './auth.config';
import { db } from '@/lib/db/queries';
import {
  user as UserTable,
  account,
  sessions,
  verificationTokens,
} from '@/lib/db/schema';
import { sendVerificationRequest } from '@/lib/auth/resend-send-verification-email';

interface ExtendedSession extends Session {
  user: User & { id: string };
}

const _ResendProvider = ResendProvider({
  apiKey: process.env.AUTH_RESEND_KEY,
  from: 'InfinityAutomate <<EMAIL>>',
  sendVerificationRequest,
});

const _AppleProvider = AppleProvider({
  clientId: process.env.APPLE_CLIENT_ID,
  clientSecret: process.env.APPLE_SECRET,
  allowDangerousEmailAccountLinking: true,
  authorization: {
    params: {
      scope: "name email state",
      response_mode: "form_post",
    },
  },
  checks: ['state'],
});

export const {
  handlers: { GET, POST },
  auth,
  signIn,
  signOut,
} = NextAuth({
  ...authConfig,
  providers: [
    Credentials({
      credentials: {},
      async authorize({ email, password }: any) {
        const users = await getUser(email);
        if (users.length === 0) return null;
        // biome-ignore lint: Forbidden non-null assertion.
        const passwordsMatch = await compare(password, users[0].password!);
        if (!passwordsMatch) return null;
        return users[0] as any;
      },
    }),
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID || '',
      clientSecret: process.env.GOOGLE_CLIENT_SECRET || '',
      allowDangerousEmailAccountLinking: true,
      authorization: {
        params: {
          prompt: 'consent',
          access_type: 'offline',
          response_type: 'code',
        },
      },
    }),
    _ResendProvider,
    _AppleProvider,
  ],
  session: {
    strategy: 'jwt',
  },
  callbacks: {
    async jwt({ token, user, account, profile }) {
      // Getting user id from database by email if user is google user
      // console.log('[Auth] jwt', account);
      if (!user?.email) return token;
      const [existUser] = await getUser(user.email);

      if (
        (account?.provider === 'google' || account?.provider === 'apple') &&
        existUser
      ) {
        token.id = existUser.id;

        token.role = existUser.role;
      } else if (
        account?.provider === 'resend' ||
        (account?.provider === 'credentials' && user?.id)
      ) {
        token.id = user.id;
        token.role = existUser ? existUser.role : 'user';
      }

      if (existUser) {
        token.name = existUser.name;
        token.image = existUser.image;
      }

      // console.log('[Auth] existUser', existUser);
      // console.log('[Auth] jwt', token);

      return token;
    },
    async session({
      session,
      token,
    }: {
      session: ExtendedSession;
      token: any;
    }) {
      // console.log('[Auth] session', session);
      if (session.user) {
        session.user.id = token.id as string;
        session.user.name = token.name;
        session.user.image = token.image;
        session.user.role = token.role;
      }

      // console.log('[Auth] session', session);

      return session;
    },
    async signIn({ user, account, profile }) {
      if (!user.email || !user.id) {
        return false;
      }

      if (
        user &&
        (account?.provider === 'google' || account?.provider === 'apple') &&
        profile?.email_verified &&
        account.expires_in
      ) {
        try {
          const [existUser] = await getUser(user.email);

          if (!existUser) {
            await createUser(
              user.email,
              user.id,
              user.name ?? undefined,
              user.image ?? undefined,
            );
          } else {
            await updateUser({
              id: user.id,
              name: user.name ?? undefined,
              image: user.image ?? undefined,
            });
          }

          return true;
        } catch (error) {
          console.error('[Auth] Google sign in error:', error);
          return false;
        }
      }

      if (user && account?.provider === 'credentials') {
        return true;
      }

      if (user && account?.provider === 'resend') {
        return true;
      }
      return false;
    },
  },
  adapter: DrizzleAdapter(db, {
    usersTable: UserTable,
    accountsTable: account,
    sessionsTable: sessions,
    verificationTokensTable: verificationTokens,
  }),
});
