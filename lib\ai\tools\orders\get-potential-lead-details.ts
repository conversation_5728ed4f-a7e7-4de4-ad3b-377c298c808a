import {
  getOrderWithItems,
  getChatByAgentModel,
  getUser,
} from '@/lib/db/queries';
import { z } from 'zod';
import { tool } from 'ai';
import type { Session } from 'next-auth';
import type { DataStreamWriter } from 'ai';
import { sql } from 'drizzle-orm';
import { db } from '@/lib/db/queries';
import { order } from '@/lib/db/schema';

interface GetPotentialLeadDetailsProps {
  session: Session;
  dataStream: DataStreamWriter;
}

const GetPotentialLeadDetailsSchema = z
  .object({
    // Поиск по ID заказа
    orderId: z.number().optional(),
    // Поиск по контактным данным получателя
    name: z.string().optional(),
    lastName: z.string().optional(),
    secondName: z.string().optional(),
    email: z.string().optional(),
    phone: z.string().optional(),
  })
  .refine(
    (data) => {
      // Хотя бы один параметр должен быть указан
      return (
        data.orderId ||
        data.name ||
        data.lastName ||
        data.secondName ||
        data.email ||
        data.phone
      );
    },
    {
      message:
        'Необходимо указать хотя бы один параметр для поиска: orderId, name, lastName, secondName, email или phone',
    },
  );

export type GetPotentialLeadDetailsInput = z.infer<
  typeof GetPotentialLeadDetailsSchema
>;

export const getPotentialLeadDetails = ({
  session,
  dataStream,
}: GetPotentialLeadDetailsProps) =>
  tool({
    description: `Получить детальную информацию о потенциальном лиде по ID заказа или контактным данным получателя. 
    Возвращает полную информацию о заказе, включая сумму, товары/услуги, контактные данные получателя и ID чатов, 
    которые были использованы для добавления OrderItem по параметру agent_model.
    
    Параметры поиска:
    - orderId: ID заказа (точный поиск)
    - name: Имя получателя (частичный поиск)
    - lastName: Фамилия получателя (частичный поиск)  
    - secondName: Отчество получателя (частичный поиск)
    - email: Email получателя (частичный поиск)
    - phone: Телефон получателя (частичный поиск)
    
    Можно использовать любую комбинацию параметров. Хотя бы один параметр обязателен.`,
    parameters: GetPotentialLeadDetailsSchema,
    execute: async ({ orderId, name, lastName, secondName, email, phone }) => {
      try {
        console.log(`[AI Tool] Getting potential lead details with params:`, {
          orderId,
          name,
          lastName,
          secondName,
          email,
          phone,
        });

        if (dataStream) {
          dataStream.writeData({
            type: 'text',
            content: 'Поиск детальной информации о потенциальном лиде...',
          });
        }

        let foundOrder: any;

        // Если указан orderId, ищем по нему
        if (orderId) {
          foundOrder = await getOrderWithItems({ id: orderId });

          if (!foundOrder) {
            const errorMessage = `Заказ с ID ${orderId} не найден`;
            console.log(`[AI Tool] ${errorMessage}`);

            if (dataStream) {
              dataStream.writeData({
                type: 'text',
                content: errorMessage,
              });
            }

            return {
              success: false,
              error: errorMessage,
              data: null,
            };
          }
        } else {
          // Поиск по контактным данным получателя
          const searchConditions: any[] = [];

          if (name) {
            searchConditions.push(
              sql`${order.recipient}::text ILIKE ${`%"name":"${name}"%`}`,
            );
          }

          if (lastName) {
            searchConditions.push(
              sql`${order.recipient}::text ILIKE ${`%"lastName":"${lastName}"%`}`,
            );
          }

          if (secondName) {
            searchConditions.push(
              sql`${order.recipient}::text ILIKE ${`%"secondName":"${secondName}"%`}`,
            );
          }

          if (email) {
            searchConditions.push(
              sql`${order.recipient}::text ILIKE ${`%"email":"${email}"%`}`,
            );
          }

          if (phone) {
            searchConditions.push(
              sql`${order.recipient}::text ILIKE ${`%"phone":"${phone}"%`}`,
            );
          }

          if (searchConditions.length === 0) {
            const errorMessage =
              'Необходимо указать хотя бы один параметр для поиска';
            console.log(`[AI Tool] ${errorMessage}`);

            if (dataStream) {
              dataStream.writeData({
                type: 'text',
                content: errorMessage,
              });
            }

            return {
              success: false,
              error: errorMessage,
              data: null,
            };
          }

          // Выполняем поиск
          const orders = await db
            .select()
            .from(order)
            .where(sql.join(searchConditions, sql` AND `))
            .limit(1);

          if (orders.length === 0) {
            const errorMessage = 'Заказ с указанными параметрами не найден';
            console.log(`[AI Tool] ${errorMessage}`);

            if (dataStream) {
              dataStream.writeData({
                type: 'text',
                content: errorMessage,
              });
            }

            return {
              success: false,
              error: errorMessage,
              data: null,
            };
          }

          // Получаем полную информацию о найденном заказе
          foundOrder = await getOrderWithItems({ id: orders[0].id });
        }

        if (!foundOrder) {
          const errorMessage =
            'Не удалось получить детальную информацию о заказе';
          console.log(`[AI Tool] ${errorMessage}`);

          if (dataStream) {
            dataStream.writeData({
              type: 'text',
              content: errorMessage,
            });
          }

          return {
            success: false,
            error: errorMessage,
            data: null,
          };
        }

        // Рассчитываем общую сумму заказа
        const totalAmount = foundOrder.items.reduce(
          (sum: number, item: any) => {
            return sum + Number.parseFloat(item.price) * item.quantity;
          },
          0,
        );

        // Получаем уникальные agent_model из OrderItem
        const agentModels = [
          ...new Set(foundOrder.items.map((item: any) => item.agentModel)),
        ];

        // Получаем чаты для каждого agent_model
        const chatDetails: Array<{
          agentModel: string;
          chatId: string;
          chatTitle: string;
        }> = [];

        const [user] = await getUser(foundOrder.recipient.email);
        if (!user) {
          const errorMessage = 'Пользователь не найден';
          console.log(`[AI Tool] ${errorMessage}`);

          if (dataStream) {
            dataStream.writeData({
              type: 'text',
              content: errorMessage,
            });
          }

          return {
            success: false,
            error: errorMessage,
            data: null,
          };
        }

        for (const agentModel of agentModels) {
          try {
            const chat = await getChatByAgentModel({
              agentModel: agentModel as string,
              userId: user.id,
            });

            if (chat) {
              chatDetails.push({
                agentModel: agentModel as string,
                chatId: chat.id,
                chatTitle: chat.title,
              });
            }
          } catch (error) {
            console.warn(
              `[AI Tool] Could not find chat for agent model ${agentModel}:`,
              error,
            );
          }
        }

        const result = {
          success: true,
          data: {
            order: {
              id: foundOrder.id,
              status: foundOrder.status,
              dateCreated: foundOrder.dateCreated,
              dateUpdated: foundOrder.dateUpdated,
              recipient: foundOrder.recipient,
            },
            items: foundOrder.items.map((item: any) => ({
              id: item.id,
              serviceId: item.serviceId,
              title: item.title,
              agentModel: item.agentModel,
              quantity: item.quantity,
              price: item.price,
              currency: item.currency,
              totalPrice: (
                Number.parseFloat(item.price) * item.quantity
              ).toFixed(2),
            })),
            summary: {
              totalAmount: totalAmount.toFixed(2),
              currency: foundOrder.items[0]?.currency || 'USD',
              itemsCount: foundOrder.items.length,
              agentModelsUsed: agentModels,
            },
            chats: chatDetails,
          },
        };

        console.log(
          `[AI Tool] Successfully retrieved lead details for order ${foundOrder.id}`,
        );

        if (dataStream) {
          dataStream.writeData({
            type: 'text',
            content: `Найдена детальная информация о заказе #${foundOrder.id}`,
          });
        }

        return result;
      } catch (error) {
        const errorMessage = `Ошибка при получении детальной информации о лиде: ${error instanceof Error ? error.message : 'Неизвестная ошибка'}`;
        console.error(`[AI Tool] ${errorMessage}`, error);

        if (dataStream) {
          dataStream.writeData({
            type: 'text',
            content: errorMessage,
          });
        }

        return {
          success: false,
          error: errorMessage,
          data: null,
        };
      }
    },
  });
