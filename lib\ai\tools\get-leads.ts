import { tool } from 'ai';
import { z } from 'zod';
// import { fetchLeadsFromBitrix } from '@/lib/bitrix'; // Раскомментируйте и реализуйте, если есть

export const getLeads = tool({
  description: 'Получить список лидов из Bitrix24',
  parameters: z.object({}), // без параметров
  async execute(args, options) {
    console.log('[getLeads] Вызов инструмента getLeads', { args, options });
    try {
      const res = await fetch('https://bitrix24-mcp-worker-production.alonalx.workers.dev/mcp/getLeads');
      if (!res.ok) {
        console.error('[getLeads] Ошибка запроса к Bitrix24:', res.status, res.statusText);
        throw new Error('Ошибка запроса к Bitrix24');
      }
      const data = await res.json();
      const leads = data.leads || data || [];
      console.log(`[getLeads] Успешно получено лидов: ${Array.isArray(leads) ? leads.length : 0}`);
      return { leads };
    } catch (e) {
      console.error('[getLeads] Ошибка при получении лидов:', e);
      return { leads: [] };
    }
  },
});
