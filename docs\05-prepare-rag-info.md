# Подготовка RAG (Retrieval-Augmented Generation)

## Пошаговая инструкция

1. **Настройка окружения**

    - **Установка интеграции Neon для Vercel**:

        - Перейдите на страницу интеграции Neon в Vercel Marketplace: [Neon для Vercel](https://vercel.com/marketplace/neon).

        - Нажмите "Install" и следуйте инструкциям для подключения вашего проекта Vercel к базе данных Neon.

        - В процессе установки выберите проект Vercel, который вы хотите подключить, и укажите соответствующие окружения (Development, Preview, Production), для которых будут добавлены переменные окружения базы данных.

        - Дополнительно, в разделе "Advanced Options", вы можете:

            - Указать префикс для переменных окружения базы данных.

            - Включить опцию создания ветки базы данных для каждого предварительного деплоя.

        - Подробнее о процессе установки: [Установка интеграции Neon Postgres на Vercel](https://neon.tech/docs/guides/vercel-native-integration).

    - **Установка Neon CLI**:

        - Убедитесь, что у вас установлен Node.js версии 18.0 или выше.

        - Установите Neon CLI глобально с помощью npm:

          ```bash
          npm install -g neonctl
          ```

        - Авторизуйтесь в Neon через веб-аутентификацию:

          ```bash
          neon auth
          ```

          Эта команда откроет браузерное окно для авторизации в вашем аккаунте Neon. Если вы используете Neon через нативную интеграцию на Vercel, рекомендуется аутентифицироваться с помощью API-ключа. Подробнее: [Neon CLI — Установка и подключение](https://neon.tech/docs/reference/cli-install).

2. **Подготовка рабочей директории**

    - Создайте отдельную директорию для хранения документов RAG:

      ```bash
      mkdir rag_documents
      ```

    - Убедитесь, что директория имеет необходимые права доступа:

      ```bash
      chmod 755 rag_documents
      ```

3. **Подготовка документов**

    - Конвертируйте все юридические документы в текстовый формат (.txt) с кодировкой UTF-8.

    - Структурируйте файлы по категориям или агентам:

      ```
      /rag_documents
        /agent1
          doc1.txt
          doc2.txt
        /agent2
          doc1.txt
          doc2.txt
      ```

    - Убедитесь, что каждый текстовый файл не превышает 1 МБ.

4. **Загрузка данных**

    - Переместите подготовленные файлы в созданную директорию:

      ```bash
      mv /path/to/your/documents/* rag_documents/
      ```

    - Проверьте корректность загруженных документов, убедившись, что все файлы доступны и структурированы правильно.

5. **Запуск обработки**

    - Перейдите в корневую директорию вашего проекта:

      ```bash
      cd /path/to/your/project
      ```

    - Выполните команду для запуска процесса RAG:

      ```bash
      npm run rag
      ```

      Для запуска в режиме разработки используйте:

      ```bash
      npm run dev:rag
      ```

    - Время выполнения зависит от объема документов.

6. **Проверка результата**

    - Дождитесь завершения процесса.

    - Проверьте логи на наличие ошибок. Логи выводятся в консоль и сохраняются в файле `logs/rag.log`:

      ```bash
      tail -f logs/rag.log
      ```

    - Убедитесь в корректности обработанных данных, проверив выходные файлы или результаты обработки.

## Дополнительные рекомендации

- **Резервное копирование**: Сделайте резервную копию документов перед началом обработки, чтобы избежать потери данных.

- **Форматирование файлов**: Убедитесь, что все текстовые файлы имеют кодировку UTF-8 и соответствуют требованиям по размеру и структуре.

- **Диагностика ошибок**: В случае возникновения ошибок:

    - Проверьте консольный вывод на наличие сообщений об ошибках.

    - Просмотрите файл логов `logs/rag.log` для детальной информации.

    - Убедитесь, что все зависимости установлены корректно и версии соответствуют требованиям.

    - Если проблема не решена, обратитесь к документации или в службу поддержки.

---

**Примечание**: Тщательное следование данной инструкции поможет обеспечить корректную работу процесса RAG и избежать возможных ошибок.
