import {getRequestConfig} from 'next-intl/server';
import { cookies } from 'next/headers';
import {LocaleCode, isValidLocaleCode} from "@/lib/i18n/languages";

export default getRequestConfig(async () => {
    const cookieStore = await cookies();
    const locale = cookieStore.get('NEXT_LOCALE')?.value || LocaleCode.EN;
    const checkedLocale = isValidLocaleCode(locale) ? locale : LocaleCode.EN;
    return {
        locale,
        messages: (await import(`@/messages/${checkedLocale}/common.json`)).default
    };
});
