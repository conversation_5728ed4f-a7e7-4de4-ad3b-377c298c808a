'use client';

import { Button } from '@/components/ui/button';
import { agentChatModels } from '@/lib/ai/models';
import Image from 'next/image';
import { useTranslations } from 'next-intl';

export function AgentList() {
  const translation_main = useTranslations('main_agent_labels');
  return (
    <div className="flex flex-col min-w-0 h-dvh bg-transparent justify-start m-4 gap-12">
      <div className="flex items-center justify-center lg:justify-between w-full">
        <div className="flex flex-col lg:flex-row items-center gap-2">
          <Image
            src="/images/Icon_agent.png"
            alt="Agent image"
            width={300}
            height={48}
            className="flex flex-row items-center gap-2 ml-[50px] mr-[50px] max-w-[150px] md:max-w-[300px]"
            priority
          />
          <span className="flex flex-col font-bold max-w-[450px] text-xl md:text-3xl lg:text-5xl lg:!leading-[70px]">
            {translation_main('choose_label')}:
          </span>
        </div>
      </div>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-x-2 lg:gap-x-6 gap-y-6 lg:gap-y-20 w-full max-w-[1280px] self-center justify-items-center ">
        {agentChatModels.map((agent, index) => {
          const translatedName = translation_main(agent.name);
          const isAvailable = agent.available;
          return (
            <div
              key={`agent-${agent.id}-${index}`}
              className={`flex flex-col items-center rounded-xl w-full max-w-[400px] ${!isAvailable ? 'opacity-50' : ''}`}
            >
              <Image
                src={agent.image}
                alt={translatedName}
                width={500}
                height={150}
                className={`rounded-lg object-cover !w-full ${isAvailable ? 'cursor-pointer' : 'cursor-not-allowed'}`}
                priority
                onClick={
                  isAvailable
                    ? async () => {
                        window.location.href = `/chat/${agent.id}`;
                      }
                    : undefined
                }
              />
              <Button
                variant="default"
                onClick={
                  isAvailable
                    ? async () => {
                        window.location.href = `/chat/${agent.id}`;
                      }
                    : undefined
                }
                disabled={!isAvailable}
                className={`w-full font-bold rounded-lg py-2 mt-2 ${!isAvailable ? 'hover:!cursor-not-allowed' : ''}`}
              >
                {translatedName}
              </Button>
            </div>
          );
        })}
      </div>
    </div>
  );
}
