// serviceFactory.ts
import "server-only"
import { RagService } from "@/lib/rag/rag.service.interface";
import { CloudflareRagService } from "@/lib/rag/cloudflare/cloudflare.service";
import { PostgresRagService } from "@/lib/rag/vectorDB/pgVector.service";
import {CloudflareSettings} from "@/lib/rag/cloudflare/cfaisearch.types";

export enum RagServiceTypes {
    CLOUDFLARE = "cloudflare",
    POSTGRES = "postgres"
}

export function createRagService(serviceType: RagServiceTypes, config?: any|CloudflareSettings): RagService {
    switch (serviceType) {
        case RagServiceTypes.CLOUDFLARE:
            return new CloudflareRagService(config.accountId, config.autoragName, config.apiToken);
        case RagServiceTypes.POSTGRES:
            return new PostgresRagService();
        default:
            throw new Error(`Неизвестный тип сервиса: ${serviceType}`);
    }
}
