
-- Drop tables if they exist (in reverse order due to dependencies)
DROP TABLE IF EXISTS "Order" CASCADE;
DROP TABLE IF EXISTS "OrderItem" CASCADE;
DROP TABLE IF EXISTS "Services" CASCADE;

-- Drop enums if they exist
DROP TYPE IF EXISTS "orderStatus" CASCADE;

-- Create orderStatus enum
CREATE TYPE "orderStatus" AS ENUM('new', 'approved');

-- Create Services table
CREATE TABLE "Services" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"reference_id" varchar(100) NOT NULL,
	"title" text NOT NULL,
	"agent_model" text,
	"category" text NOT NULL,
	"cost" varchar(20) NOT NULL,
	"currency" varchar(3) NOT NULL,
	"required_prepaid" boolean DEFAULT false NOT NULL,
	"prepaid_amount" varchar(20) DEFAULT '0' NOT NULL
);

-- Create OrderItem table
CREATE TABLE "OrderItem" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"order_id" integer NOT NULL,
	"service_id" varchar(100) NOT NULL,
	"title" text NOT NULL,
	"agent_model" text NOT NULL,
	"quantity" integer NOT NULL,
	"price" varchar(20) NOT NULL,
	"currency" varchar(3) NOT NULL
);

-- Create Order table
CREATE TABLE "Order" (
	"id" integer PRIMARY KEY GENERATED ALWAYS AS IDENTITY NOT NULL,
	"status" "orderStatus" DEFAULT 'new' NOT NULL,
	"recipient" json NOT NULL,
	"date_created" timestamp with time zone DEFAULT now() NOT NULL,
	"date_updated" timestamp with time zone DEFAULT now() NOT NULL
);

-- Add foreign key constraint
ALTER TABLE "OrderItem" ADD CONSTRAINT "OrderItem_order_id_Order_id_fk" FOREIGN KEY ("order_id") REFERENCES "Order"("id") ON DELETE CASCADE;

-- Create indexes for better performance
CREATE INDEX "idx_services_reference_id" ON "Services" ("reference_id");
CREATE INDEX "idx_order_status" ON "Order" ("status");
CREATE INDEX "idx_order_date_created" ON "Order" ("date_created");
CREATE INDEX "idx_orderitem_service_id" ON "OrderItem" ("service_id");
CREATE INDEX "idx_orderitem_order_id" ON "OrderItem" ("order_id");