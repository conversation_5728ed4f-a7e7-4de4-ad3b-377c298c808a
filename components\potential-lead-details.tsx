'use client';

import React from 'react';
import { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from '@/components/ui/sheet';
import {
  MessageSquareIcon,
  CalendarIcon,
  UserIcon,
  PhoneIcon,
  ShoppingCartIcon,
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { de, enUS, ru } from 'date-fns/locale';
import { PreviewMessage } from '@/components/message';
import type { UIMessage } from 'ai';
import { useCookies } from 'next-client-cookies';

interface PotentialLeadDetailsProps {
  data: {
    order: {
      id: number;
      status: string;
      dateCreated: string;
      dateUpdated: string;
      recipient: {
        name: string;
        lastName: string;
        secondName?: string;
        email: string;
        phone: string;
        comments?: string;
      };
    };
    items: Array<{
      id: string;
      serviceId: string;
      title: string;
      agentModel: string;
      quantity: number;
      price: string;
      currency: string;
      totalPrice: string;
    }>;
    summary: {
      totalAmount: string;
      currency: string;
      itemsCount: number;
      agentModelsUsed: string[];
    };
    chats: Array<{
      agentModel: string;
      chatId: string;
      chatTitle: string;
    }>;
  };
}

interface ChatModalProps {
  chatId: string;
  leadName: string;
  chatTitle: string;
  agentModel: string;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
}

function ChatModal({
  chatId,
  leadName,
  chatTitle,
  agentModel,
  isOpen,
  onOpenChange,
}: ChatModalProps) {
  const t = useTranslations('potential_leads.details');
  const [messages, setMessages] = useState<UIMessage[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (isOpen && chatId) {
      setLoading(true);
      fetch(`/api/chat/${chatId}/messages`)
        .then((response) => response.json())
        .then((data) => {
          if (data.success && data.messages) {
            // Преобразуем сообщения из БД в формат UIMessage
            const uiMessages: UIMessage[] = data.messages
              .slice(1)
              .map((msg: any) => ({
                id: msg.id,
                role: msg.role as 'user' | 'assistant',
                content: '', // Добавляем пустое содержимое для совместимости
                parts: Array.isArray(msg.parts) ? msg.parts : [],
                createdAt: msg.createdAt,
              }));
            setMessages(uiMessages);
          } else {
            console.error('Failed to load chat messages:', data.error);
          }
        })
        .catch((error) => {
          console.error('Failed to load chat messages:', error);
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [isOpen, chatId]);

  return (
    <Sheet open={isOpen} onOpenChange={onOpenChange}>
      <SheetContent className="sm:max-w-1/3 w-1/3 max-h-[80vh] flex flex-col">
        <SheetHeader>
          <SheetTitle className="flex items-center gap-2">
            <MessageSquareIcon className="h-5 w-5" />
            {leadName}
            <span className="px-2 py-1 bg-gray-100 text-gray-800 rounded text-xs">
              {t('related_chats')}
            </span>
          </SheetTitle>
          <SheetDescription>{agentModel}</SheetDescription>
        </SheetHeader>

        <div className="flex-1 overflow-hidden">
          {loading ? (
            <div className="flex items-center justify-center h-32">
              <div className="text-muted-foreground">
                {t('loading_messages')}
              </div>
            </div>
          ) : (
            <div className="h-full overflow-auto">
              <MessagesHistory messages={messages} chatId={chatId} />
            </div>
          )}
        </div>
      </SheetContent>
    </Sheet>
  );
}

function MessagesHistory({
  messages,
  chatId,
}: {
  messages: UIMessage[];
  chatId: string;
}) {
  const t = useTranslations('potential_leads.details');
  return (
    <div className="flex flex-col min-w-0 gap-6 flex-1 overflow-auto pt-4">
      {messages.length === 0 && (
        <div className="flex items-center justify-center h-32 text-muted-foreground">
          {t('no_messages')}
        </div>
      )}
      {messages.map((message, index) => (
        <PreviewMessage
          key={message.id}
          chatId={chatId}
          messages={messages}
          message={message}
          isLoading={false}
          vote={undefined}
          setMessages={() => {}}
          reload={() => Promise.resolve(null)}
          isReadonly={true}
          append={() => Promise.resolve(null)}
        />
      ))}
    </div>
  );
}

export function PotentialLeadDetails({ data }: PotentialLeadDetailsProps) {
  const { order, items, summary, chats } = data;
  const { recipient } = order;
  const t = useTranslations('potential_leads.details');
  const cookies = useCookies();
  const locale = cookies.get('NEXT_LOCALE') || 'en';

  // Состояние для модального окна просмотра чата
  const [selectedChat, setSelectedChat] = useState<{
    chatId: string;
    leadName: string;
    chatTitle: string;
    agentModel: string;
  } | null>(null);

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'new':
        return 'bg-blue-100 text-blue-800';
      case 'approved':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'new':
        return t('status_new');
      case 'approved':
        return t('status_approved');
      default:
        return status;
    }
  };

  return (
    <div className="space-y-6">
      {/* Заголовок */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">
          {t('title')} #{order.id}
        </h2>
        <span
          className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusBadgeVariant(order.status)}`}
        >
          {getStatusText(order.status)}
        </span>
      </div>

      {/* Информация о заказе */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ShoppingCartIcon className="h-5 w-5" />
            {t('order_info')}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center gap-2 text-sm">
              <CalendarIcon className="h-4 w-4 text-muted-foreground" />
              <span className="text-muted-foreground">{t('created')}:</span>
              <span>
                {formatDistanceToNow(new Date(order.dateCreated), {
                  addSuffix: true,
                  locale: locale === 'ru' ? ru : locale === 'de' ? de : enUS,
                })}
              </span>
            </div>
            <div className="flex items-center gap-2 text-sm">
              <CalendarIcon className="h-4 w-4 text-muted-foreground" />
              <span className="text-muted-foreground">{t('updated')}:</span>
              <span>
                {formatDistanceToNow(new Date(order.dateUpdated), {
                  addSuffix: true,
                  locale: locale === 'ru' ? ru : locale === 'de' ? de : enUS,
                })}
              </span>
            </div>
          </div>

          <Separator />

          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-primary">
                {summary.itemsCount}
              </div>
              <div className="text-sm text-muted-foreground">
                {t('services_count')}
              </div>
            </div>
            <div>
              <div className="text-2xl font-bold text-green-600">
                {summary.totalAmount} {summary.currency}
              </div>
              <div className="text-sm text-muted-foreground">
                {t('total_amount')}
              </div>
            </div>
            <div>
              <div className="text-2xl font-bold text-blue-600">
                {summary.agentModelsUsed.length}
              </div>
              <div className="text-sm text-muted-foreground">
                {t('ai_agents_count')}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Контактная информация */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <UserIcon className="h-5 w-5" />
            {t('contact_info')}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <div className="text-sm text-muted-foreground">
                {t('full_name')}
              </div>
              <div className="font-medium">
                {[recipient.name, recipient.secondName, recipient.lastName]
                  .filter(Boolean)
                  .join(' ') || t('not_available')}
              </div>
            </div>
            <div>
              <div className="text-sm text-muted-foreground">Email</div>
              <div className="flex items-center gap-2">
                <div className="font-medium">
                  {recipient.email ? (
                    <a
                      href={`mailto:${recipient.email}`}
                      className="text-blue-600 hover:underline"
                    >
                      {recipient.email}
                    </a>
                  ) : (
                    t('not_available')
                  )}
                </div>
              </div>
            </div>
            <div>
              <div className="text-sm text-muted-foreground">{t('phone')}</div>
              <div className="flex items-center gap-2">
                <PhoneIcon className="h-4 w-4 text-muted-foreground" />
                <a
                  href={`tel:${recipient.phone}`}
                  className="text-blue-600 hover:underline"
                >
                  {recipient.phone}
                </a>
              </div>
            </div>
            {recipient.comments && (
              <div>
                <div className="text-sm text-muted-foreground">
                  {t('comments')}
                </div>
                <div className="text-sm bg-muted p-2 rounded">
                  {recipient.comments}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Товары/услуги */}
      <Card>
        <CardHeader>
          <CardTitle>{t('services')}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {items.map((item) => (
              <div key={item.id} className="border rounded-lg p-4">
                <div className="flex justify-between items-start mb-2">
                  <div className="flex-1">
                    <h4 className="font-medium">{item.title}</h4>
                    <div className="text-sm text-muted-foreground">
                      Service ID: {item.serviceId}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      AI Agent:{' '}
                      <span className="px-2 py-1 bg-gray-100 text-gray-800 rounded text-xs">
                        {item.agentModel}
                      </span>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-medium">
                      {item.totalPrice} {item.currency}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {item.price} × {item.quantity}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Связанные чаты */}
      {chats.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MessageSquareIcon className="h-5 w-5" />
              {t('related_chats')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {chats.map((chat) => (
                <div
                  key={chat.chatId}
                  className="flex items-center justify-between p-3 border rounded-lg"
                >
                  <div>
                    <div className="font-medium">{t('related_chats')}</div>
                    <div className="text-sm text-muted-foreground">
                      AI Agent:{' '}
                      <span className="px-2 py-1 bg-gray-100 text-gray-800 rounded text-xs">
                        {chat.agentModel}
                      </span>
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() =>
                      setSelectedChat({
                        chatId: chat.chatId,
                        leadName: `${recipient.name} ${recipient.lastName}`,
                        chatTitle: chat.chatTitle,
                        agentModel: chat.agentModel,
                      })
                    }
                  >
                    {t('view_chat')}
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
      {selectedChat && (
        <ChatModal
          leadName={`${order.recipient.name} ${order.recipient.lastName} ${order.recipient.secondName}`}
          chatId={selectedChat.chatId}
          chatTitle={selectedChat.chatTitle}
          agentModel={selectedChat.agentModel}
          isOpen={!!selectedChat}
          onOpenChange={(open) => !open && setSelectedChat(null)}
        />
      )}
    </div>
  );
}
