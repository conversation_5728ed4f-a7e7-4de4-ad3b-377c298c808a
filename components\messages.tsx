import type { UIMessage } from 'ai';
import { PreviewMessage, ThinkingMessage } from './message';
import { Overview } from './overview';
import { memo, useEffect } from 'react';
import type { Vote } from '@/lib/db/schema';
import equal from 'fast-deep-equal';
import type { UseChatHelpers } from '@ai-sdk/react';
import { motion } from 'framer-motion';
import { useMessages } from '@/hooks/use-messages';
import { MODERATOR_CHAT_MODEL } from '@/lib/ai/models';

interface MessagesProps {
  chatId: string;
  status: UseChatHelpers['status'];
  votes: Array<Vote> | undefined;
  messages: Array<UIMessage>;
  setMessages: UseChatHelpers['setMessages'];
  reload: UseChatHelpers['reload'];
  isReadonly: boolean;
  isArtifactVisible: boolean;
  selectedChatModel: string;
  append: UseChatHelpers['append'];
}

function PureMessages({
  chatId,
  status,
  votes,
  messages,
  setMessages,
  reload,
  isReadonly,
  selectedChatModel,
  append,
}: MessagesProps) {
  const {
    containerRef: messagesContainerRef,
    endRef: messagesEndRef,
    onViewportEnter,
    onViewportLeave,
    hasSentMessage,
    scrollToBottom,
    isAtBottom,
  } = useMessages({
    chatId,
    status,
  });

  // Автоматическая прокрутка при изменении сообщений, если пользователь находится внизу
  useEffect(() => {
    if (isAtBottom || status === 'streaming') {
      scrollToBottom();
    }
  }, [messages.length, status, isAtBottom, scrollToBottom]);

  return (
    <div
      ref={messagesContainerRef}
      id={`messages-container-${chatId}`}
      className="flex flex-col min-w-0 gap-6 flex-1 overflow-auto pt-4 relative"
    >
      {messages.length === 0 && (
        <Overview selectedChatModel={selectedChatModel} />
      )}

      {messages
        .slice(selectedChatModel === MODERATOR_CHAT_MODEL.id ? 0 : 1)
        .map((message, index) => (
          <PreviewMessage
            key={message.id}
            chatId={chatId}
            message={message}
            messages={messages}
            isLoading={status === 'streaming' && messages.length - 1 === index}
            vote={
              votes
                ? votes.find((vote) => vote.messageId === message.id)
                : undefined
            }
            setMessages={setMessages}
            reload={reload}
            append={append}
            isReadonly={isReadonly}
            requiresScrollPadding={
              hasSentMessage && index === messages.length - 1
            }
            agent={selectedChatModel}
          />
        ))}

      {status === 'submitted' &&
        messages.length > 0 &&
        messages[messages.length - 1].role === 'user' && <ThinkingMessage />}

      <motion.div
        ref={messagesEndRef}
        className="shrink-0 min-w-[24px] min-h-[24px]"
        onViewportLeave={onViewportLeave}
        onViewportEnter={onViewportEnter}
      />
    </div>
  );
}

export const Messages = memo(PureMessages, (prevProps, nextProps) => {
  if (prevProps.isArtifactVisible && nextProps.isArtifactVisible) return true;

  if (prevProps.status !== nextProps.status) return false;
  if (prevProps.status && nextProps.status) return false;
  if (prevProps.messages.length !== nextProps.messages.length) return false;
  if (!equal(prevProps.messages, nextProps.messages)) return false;
  if (!equal(prevProps.votes, nextProps.votes)) return false;

  return true;
});
