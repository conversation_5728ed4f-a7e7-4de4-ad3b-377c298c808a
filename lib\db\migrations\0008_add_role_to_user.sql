DO $$
BEGIN
    -- Удаляем столбец role, если он есть
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name='User' AND column_name='role'
    ) THEN
        ALTER TABLE "User" DROP COLUMN "role";
    END IF;
END $$;

-- Удаляем тип, если он есть
DROP TYPE IF EXISTS "public"."userRole";

-- Создаём тип заново
CREATE TYPE "public"."userRole" AS ENUM('user', 'admin', 'moderator');

-- Добавляем столбец role обратно, если нужно
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name='User' AND column_name='role'
    ) THEN
        ALTER TABLE "User" ADD COLUMN "role" varchar(10) DEFAULT 'user' NOT NULL;
    END IF;
END $$;
--> statement-breakpoint
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name='User' AND column_name='role'
    ) THEN
        ALTER TABLE "User" ADD COLUMN "role" varchar(10) DEFAULT 'user' NOT NULL;
    END IF;
END $$;