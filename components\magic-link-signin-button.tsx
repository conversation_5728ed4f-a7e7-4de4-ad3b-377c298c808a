'use client';

import { useTranslations } from 'next-intl';
import { useSearchParams } from 'next/navigation';

export function MagicLinkSignInButton({
  toggleForm,
}: {
  toggleForm: () => void;
}) {
  const searchParams = useSearchParams();
  const callbackUrl = searchParams.get('callbackUrl');

  const t = useTranslations('login_page');

  const handleSignIn = async () => {
    try {
      toggleForm();
    } catch (error) {
      console.error('[Auth] Failed to sign in with Magic Link:', error);
    }
  };

  return (
    <button
      type="button"
      onClick={handleSignIn}
      className="flex items-center justify-center"
    >
      <img
        src="/images/magic-link-icon.png"
        alt=""
        className="drop-shadow-[3px_2px_2px_rgba(0,0,0,0.7)] hover:drop-shadow-[-3px_1px_1px_rgba(0,0,0,0.7)] hover:scale-95 w-[50px] h-[50px]"
      />
    </button>
  );
}
