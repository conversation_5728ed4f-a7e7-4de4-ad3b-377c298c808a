import { AgentList } from '@/components/agent-list';
import { ChatHeader } from '@/components/chat-header';
import {useMessages, useTranslations} from 'next-intl';

export default async function Page() {
  return (
    <>
      <div
        className="flex flex-col min-w-0 h-dvh bg-transparent bg-[url('/images/Background.png')] bg-no-repeat bg-cover bg-center overflow-y-auto"
        style={{
          backgroundSize: '110% 98%',
        }}
      >
        <ChatHeader />
        <AgentList />
      </div>
    </>
  );
}
