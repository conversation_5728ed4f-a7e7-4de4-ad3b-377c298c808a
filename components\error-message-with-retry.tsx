'use client';

import type { UIMessage } from 'ai';
import type { UseChatHelpers } from '@ai-sdk/react';
import { Button } from './ui/button';
import { LoaderIcon, RedoIcon, WarningIcon } from './icons';
import { deleteTrailingMessages } from '@/app/(chat)/actions';

// Компонент для отображения сообщения об ошибке с кнопкой повтора
interface ErrorMessageWithRetryProps {
  errorMessage: string;
  isSubmitRetryTool: boolean;
  setIsSubmitRetryTool: (value: boolean) => void;
  message: UIMessage;
  messages: UIMessage[];
  setMessages: UseChatHelpers['setMessages'];
  reload: () => void;
}

export function ErrorMessageWithRetry({
  errorMessage,
  isSubmitRetryTool,
  setIsSubmitRetryTool,
  message,
  messages,
  setMessages,
  reload,
}: ErrorMessageWithRetryProps) {
  return (
    <div className="flex flex-row gap-2 items-center justify-start text-red-500 rounded-xl p-4 bg-white ">
      <div className="flex flex-row gap-2 grow-0">
        <WarningIcon size={16} />
      </div>
      <div className="flex flex-row gap-2 grow-0 basis-full">
        {errorMessage}
      </div>

      <div className="flex flex-row gap-2 grow-1 basis-full justify-end">
        <Button
          data-testid="message-editor-send-button"
          variant="ghost"
          className="h-fit py-2 px-3 text-primary"
          disabled={isSubmitRetryTool}
          onClick={async () => {
            setIsSubmitRetryTool(true);

            const curIndex = messages.findIndex((m) => m.id === message.id);

            if (curIndex === -1) {
              return messages;
            }

            const userMessageIndex = curIndex - 1;
            const userMessage = messages[userMessageIndex];

            if (userMessageIndex !== -1) {
              await deleteTrailingMessages({
                id: userMessage.id,
              });
            }

            setMessages((messages) => {
              if (userMessageIndex !== -1) {
                const updatedMessage = {
                  ...userMessage,
                  content: userMessage.content,
                  parts: userMessage.parts,
                };

                return [...messages.slice(0, userMessageIndex), updatedMessage];
              }

              return messages;
            });

            reload();
          }}
        >
          {isSubmitRetryTool ? (
            <div className="animate-spin">
              <LoaderIcon size={16} />
            </div>
          ) : (
            <RedoIcon size={16} />
          )}
        </Button>
      </div>
    </div>
  );
}
