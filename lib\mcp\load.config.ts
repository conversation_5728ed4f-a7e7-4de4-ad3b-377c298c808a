//./lib/mcp/load.config.ts
import fs from 'fs';
import path from 'path';
import { z } from 'zod';

// Updated interface: sseUrl is now optional
export interface MCPConnectionOptions {
  id: string;
  transportType: "stdio" | "sse" | "streamable-http" | "websocket" | "unknown";
  command: string;
  args: string[];
  sseUrl?: string; // <-- changed to optional
  env?: Record<string, string>;
  bearerToken?: string;
  headerName?: string;
}

interface McpServersConfig {
  mcpServers: {
    [key: string]: MCPConnectionOptions;
  };
}

// Zod schema remains unchanged
const MCPConnectionOptionsSchema = z.object({
  id: z.string(),
  transportType: z.union([
    z.literal('stdio'),
    z.literal('sse'),
    z.literal('streamable-http'),
    z.literal('websocket'),
    z.literal('unknown')
  ]),
  command: z.string(),
  args: z.array(z.string()),
  sseUrl: z.string().url().optional(),
  env: z.record(z.string()).optional(),
  bearerToken: z.string().optional(),
  headerName: z.string().optional()
});

const McpServersConfigSchema = z.object({
  mcpServers: z.record(MCPConnectionOptionsSchema)
});

export async function loadMcpConfig(): Promise<McpServersConfig|null> {
  let configContent: string;

  if (process.env.MCP_CONF) {
    configContent = process.env.MCP_CONF;
  } else if (process.env.MCP_CONFIG_URL) {
    const response = await fetch(process.env.MCP_CONFIG_URL);
    if (!response.ok) {
      throw new Error(`Failed to fetch MCP config from ${process.env.MCP_CONFIG_URL}`);
    }
    configContent = await response.text();
  } else {
    const configPath = process.env.MCP_CONFIG_PATH || path.resolve(process.cwd(), 'config.json');
    if (!fs.existsSync(configPath)) {
      throw new Error(`MCP config not found at ${configPath}`);
    }
    configContent = fs.readFileSync(configPath, 'utf-8');
  }

  try{
    const parsed = JSON.parse(configContent);
    const validated = McpServersConfigSchema.parse(parsed);
    console.log('MCP config loaded:', validated);
    return validated;
  }catch (error) {
    console.error('Error parsing MCP config:', error);
    return null;
  }
}

export function getMCPConfigList(value: McpServersConfig): MCPConnectionOptions [] {
  if (!value || value===null) return [];
  return Object.values(value.mcpServers);
}