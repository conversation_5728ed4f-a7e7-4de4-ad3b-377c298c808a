import React, { useActionState, useEffect, useState } from 'react';
import Form from 'next/form';

import { Input } from './ui/input';
import { Label } from './ui/label';
import { useTranslations } from 'next-intl';
import { type LoginActionState, signInWithResend } from '@/app/(auth)/actions';
import { useRouter, useSearchParams } from 'next/navigation';
import { toast } from '@/components/toast';
import { getAuthErrorMessageByCode } from '@/lib/utils';
import { SubmitButton } from './submit-button';
import Link from 'next/link';
import { Button } from './ui/button';
import { ArrowLeft } from 'lucide-react';

export function ResendAuthForm({
  defaultEmail = '',
  offerAccepted,
  setOfferAccepted,
  toggleMagicLinkForm,
}: {
  defaultEmail?: string;
  offerAccepted?: boolean;
  setOfferAccepted?: (value: boolean) => void;
  toggleMagicLinkForm?: () => void;
}) {
  const router = useRouter();
  const translation_login_page = useTranslations('login_page');
  const translation_terms_use = useTranslations('agreements');
  const [isSuccessful, setIsSuccessful] = useState(false);
  const [email, setEmail] = useState(defaultEmail);
  const searchParams = useSearchParams();
  const errorCode = searchParams.get('error');
  const callbackUrl = searchParams.get('callbackUrl');
  const errorMessage = getAuthErrorMessageByCode(errorCode);
  const [state, formAction] = useActionState<LoginActionState, FormData>(
    signInWithResend,
    {
      status: 'idle',
    },
  );

  useEffect(() => {
    if (state.status === 'failed') {
      toast({
        type: 'error',
        description: translation_login_page('error_invalid_credentials'),
      });
    } else if (state.status === 'invalid_data') {
      toast({
        type: 'error',
        description: translation_login_page('error_invalid_data'),
      });
    } else if (errorCode) {
      toast({
        type: 'error',
        description: errorMessage?.description,
      });
    } else if (state.status === 'success') {
      setIsSuccessful(true);
      router.push(`/sent?email=${encodeURIComponent(email)}`);
    }
  }, [state.status, router]);

  const handleSubmit = (formData: FormData) => {
    setEmail(formData.get('email') as string);
    formAction(formData);
  };

  return (
    <Form
      action={handleSubmit}
      className="flex flex-col gap-4 px-4 sm:px-16 text-gray-300"
    >
      <div className="flex flex-col gap-2">
        <Label htmlFor="email" className="font-normal text-xs md:text-sm">
          {translation_login_page('email')}
        </Label>

        <Input
          id="email"
          name="email"
          className="bg-muted text-md md:text-sm text-black"
          type="email"
          placeholder="<EMAIL>"
          autoComplete="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          required
          autoFocus
        />
        <Input
          id="callbackUrl"
          name="callbackUrl"
          className="bg-muted text-md md:text-sm"
          type="hidden"
          value={callbackUrl || ''}
        />
      </div>

      {setOfferAccepted && (
        <div className="flex items-center gap-2 my-4">
          <input
            type="checkbox"
            id="offer"
            checked={offerAccepted}
            onChange={(e) => setOfferAccepted?.(e.target.checked)}
            className="accent-primary transform scale-125"
            required
          />
          <Label htmlFor="offer" className="text-xs cursor-pointer mt-0">
            {translation_terms_use('label_agreements')}{' '}
            <Link
              href="#"
              className="text-xs font-semibold text-white hover:underline"
            >
              {translation_terms_use('link_agreements')}
            </Link>
          </Label>
        </div>
      )}

      <SubmitButton
        isSuccessful={isSuccessful}
        disabled={!offerAccepted || email.trim().length === 0}
      >
        {translation_login_page('login_button')}
      </SubmitButton>

      <Button
        variant="link"
        type="button"
        size="sm"
        className="text-white !hover:text-black p-8 "
        onClick={() => toggleMagicLinkForm?.()}
      >
        <ArrowLeft className="flex !w-[30px] !h-[30px] text-white !hover:text-black" />{' '}
        {translation_login_page('back')}
      </Button>
    </Form>
  );
}
