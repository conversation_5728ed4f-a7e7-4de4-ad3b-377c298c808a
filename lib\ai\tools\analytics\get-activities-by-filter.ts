import { tool, type DataStreamWriter } from 'ai';
import { z } from 'zod';
import { BetaAnalyticsDataClient } from '@google-analytics/data';
import type { Session } from 'next-auth';
import { subDays, format } from 'date-fns';

const analyticsDataClient = new BetaAnalyticsDataClient({
  keyFilename: process.env.GCLOUD_CREDS_PATH,
});

function getDateRange(durationDays: number = 30): { startDate: string; endDate: string } {
  const today = new Date();
  const endDate = format(today, 'yyyy-MM-dd'); // "YYYY-MM-DD"
  const pastDate = subDays(today, durationDays);
  const startDate = format(pastDate, 'yyyy-MM-dd');

  return { startDate, endDate };
}

interface ChatActivityMetricsProps {
  session: Session;
  dataStream: DataStreamWriter;
}

function getDateRange30Days(): { startDate: string; endDate: string } {
  const today = new Date();

  // Текущая дата (конец диапазона)
  const endDate = today.toISOString().slice(0, 10); // "YYYY-MM-DD"

  // Дата 30 дней назад
  const past = new Date();
  past.setDate(past.getDate() - 30); // вычитает 30 дней :contentReference[oaicite:0]{index=0}
  const startDate = past.toISOString().slice(0, 10);

  return { startDate, endDate };
}

// Список поддерживаемых агентов
const SUPPORTED_AGENTS = {
  'chat-model-vnj': 'Агент по виду на жительство (Германия)',
  'chat-model-visa': 'Агент по оформлению визы',
  'chat-model-pmj': 'Агент по оформлению ПМЖ',
  'chat-model-residence': 'Агент по оформлению гражданства',
  'chat-model-diplom': 'Агент по признанию диплома',
  'chat-model-bussiness': 'Агент по бизнес-иммиграции',
  'chat-model-freelance': 'Агент по оформлению статуса фрилансера',
  'chat-model-movement': 'Агент по вопросам релокации',
} as const;

type AgentKey = keyof typeof SUPPORTED_AGENTS;

const SUPPORTED_METRICS = {
  send_message: 'Количество отправленных сообщений',
  return_user: 'Количество вернувшихся пользователей',
} as const;


type MetricKey = keyof typeof SUPPORTED_METRICS;


export const getChatActivityMetrics = ({session, dataStream}:ChatActivityMetricsProps) => tool({
  description:
    `
    Получить количество событий для указанной метрики и агента за заданный период.
    ВАЖНО: Обязательные поля: agent, metricName.
    Доступные агенты:
    ${Object.entries(SUPPORTED_AGENTS)
      .map(([key, desc]) => ` - ${key}: ${desc}`)
      .join('\n')}
    Доступные метрики:
    ${Object.entries(SUPPORTED_METRICS)
      .map(([key, desc]) => ` - ${key}: ${desc}`)
    }
   ВАЖНО: Если не указана длительность периода то вернуть 30 дней.
    `    ,
  parameters: z.object({
    agent: z.string().describe(`Имя агента для для информации.\
      Доступные агенты:\
            ${Object.entries(SUPPORTED_AGENTS)
            .map(([key, desc]) => ` - ${key}: ${desc}`)
            .join('\n')}\
            ***ВАЖНО: Если не указан агент, то вернуть agent как undefined. Ты не должен выбирать за пользователя этот параметр.
            `),
    metricName: z.string().describe(`Имя метрики для анализа.\
      Доступные метрики: 
      ${Object.entries(SUPPORTED_METRICS)
        .map(([key, desc]) => ` - ${key}: ${desc}`)
        }
      ***ВАЖНО: Если не указана метрика, то вернуть metricName как undefined. Ты не должен выбирать за пользователя этот параметр`),

    durationDays: z
      .number()
      .int()
      .positive()
      .default(30)
      .describe(`Период анализа в днях, например: 7, 14, 30\
        ***ВАЖНО: Если не указана длительность периода то вернуть 30 дней.`),
  }),
  execute: async ({ agent, metricName, durationDays }) => {
    try {
          if (!session?.user) {
            dataStream.writeData({
              type: 'text',
              content: 'Unauthorized',
            });

            return 'Unauthorized';
          }

          if(!agent) return Error("No agent selected");
          if(!metricName) return Error("No metric selected");
          
          console.log(agent, metricName, durationDays)

          dataStream.writeData({
            type: 'text',
            content: 'Анализирую данные по текущему агенту...',
          });
        console.log(`Getting chat activity metrics for agent ${agent}`);
        const metricKey = metricName as MetricKey;
        let metricsParams = {};
        const { startDate, endDate } = getDateRange(durationDays); 
        switch (metricKey) { 
          case 'return_user':
            metricsParams = ReturnUserMetricParams(startDate, endDate, metricKey, agent);
            break;
          case 'send_message':
            metricsParams = SendMessageMetricParams(startDate, endDate, metricKey, agent);
            break;
          default:
            throw new Error(`Unsupported metric ${metricName}`);
        }
        const [response] = await analyticsDataClient.runReport(metricsParams);

      console.log(response);
      const result = response.rows?.map(row => {
                                                const eventName = row.dimensionValues?.[0]?.value ?? 'unknown';
                                                const count = Number(row.metricValues?.[0]?.value ?? 0);
                                                return { [eventName]: count };
                                              }) ?? [];
        return {data: result, success: true};
    } catch (error) {
      console.error('Error calling Analytics:', error);
      return Error("Analytics error on call");
    }
  }
});

const SendMessageMetricParams = (startDate, endDate, metricName, agent) => {
  return {
          property: `properties/${process.env.PROPERTIE_ID}`, // ← подставь свой ID
          dateRanges: [{ startDate: startDate, endDate: endDate }],
            dimensions: [
              { name: "eventName" },
              {name: "customEvent:agent"}
            ],
            metrics: [{ name: "eventCount" }],
            dimensionFilter: {
              andGroup: {
                    expressions: [
                      {
                        filter: {
                          fieldName: "eventName",
                          stringFilter: {
                            matchType: "EXACT",
                            value: metricName
                          }
                        }
                      },
                      {
                        notExpression: {
                          filter: {
                            fieldName: "customEvent:agent",
                            stringFilter: {
                              matchType: "EXACT",
                              value: "(not set)"
                            }
                          }
                        }
                      },
                      {
                        filter: {
                          fieldName: "customEvent:agent",
                          stringFilter: {
                            matchType: "EXACT",
                            value: agent
                          }
                        }
                      }
                    ]
                  }
            }
        }
      };

const ReturnUserMetricParams = (startDate, endDate, metricName, agent) => {
  return {
          property: `properties/${process.env.PROPERTIE_ID}`, // ← подставь свой ID
          dateRanges: [{ startDate: startDate, endDate: endDate }],
            dimensions: [
              { name: "eventName" },
              {name: "customEvent:agent"}
            ],
            metrics: [{ name: "totalUsers" }],
            dimensionFilter: {
              andGroup: {
                    expressions: [
                      {
                        filter: {
                          fieldName: "eventName",
                          stringFilter: {
                            matchType: "EXACT",
                            value: metricName
                          }
                        }
                      },
                      {
                        notExpression: {
                          filter: {
                            fieldName: "customEvent:agent",
                            stringFilter: {
                              matchType: "EXACT",
                              value: "(not set)"
                            }
                          }
                        }
                      },
                      {
                        filter: {
                          fieldName: "customEvent:agent",
                          stringFilter: {
                            matchType: "EXACT",
                            value: agent
                          }
                        }
                      }
                    ]
                  }
            }
        }
      };