'use client';

import { motion } from 'framer-motion';
import { Button } from './ui/button';
import { memo } from 'react';
import { useTranslations } from 'next-intl';
import type { UseChatHelpers } from '@ai-sdk/react';
import { AGENT_CHAT_MODELS, MODERATOR_CHAT_MODEL } from '@/lib/ai/models';

interface SuggestedActionsProps {
  append: UseChatHelpers['append'];
  agentModel: string;
}

const visaActions = [
  {
    title: 'Национальная виза',
    label: 'Получение национальной визы',
    action: 'Как получить национальную визу в Германию?',
  },
  {
    title: 'Шенген',
    label: 'Получение шенгенской визы',
    action: 'Как получить шенгенскую визу в Германию?',
  },
  {
    title: 'Воссоединение',
    label: 'Виза для воссоединения семьи',
    action: 'Как получить визу для воссоединения семьи в Германии?',
  },
  {
    title: 'Рабочая виза',
    label: 'Получение рабочей визы',
    action: 'Как получить рабочую визу в Германию?',
  },
];

const vnjActions = [
  {
    title: 'Blue Card',
    label: 'Получение Blue Card',
    action: 'Как получить Blue Card в Германии?',
  },
  {
    title: 'Freelancer',
    label: 'ВНЖ для фрилансеров',
    action: 'Как получить ВНЖ для фрилансеров в Германии?',
  },
  {
    title: 'Учеба',
    label: 'ВНЖ для учебы',
    action: 'Как получить студенческий ВНЖ в Германии?',
  },
  {
    title: 'Работа',
    label: 'ВНЖ для работы',
    action: 'Как получить рабочий ВНЖ в Германии?',
  },
];

const pmjActions = [
  {
    title: 'Условия',
    label: 'Условия получения ПМЖ',
    action: 'Какие условия для получения ПМЖ в Германии?',
  },
  {
    title: 'Сроки',
    label: 'Сроки получения ПМЖ',
    action: 'Через сколько лет можно получить ПМЖ в Германии?',
  },
  {
    title: 'Документы',
    label: 'Необходимые документы',
    action: 'Какие документы нужны для получения ПМЖ в Германии?',
  },
  {
    title: 'Язык',
    label: 'Языковые требования',
    action: 'Какой уровень немецкого нужен для ПМЖ?',
  },
];

const residenceActions = [
  {
    title: 'Гражданство',
    label: 'Получение гражданства',
    action: 'Как получить гражданство Германии?',
  },
  {
    title: 'Условия',
    label: 'Условия натурализации',
    action: 'Какие условия для получения гражданства Германии?',
  },
  {
    title: 'Двойное',
    label: 'Двойное гражданство',
    action: 'Возможно ли двойное гражданство с Германией?',
  },
  {
    title: 'Тест',
    label: 'Тест на гражданство',
    action: 'Что нужно знать для сдачи теста на гражданство Германии?',
  },
];

function PureSuggestedActions({ append, agentModel }: SuggestedActionsProps) {
  const t = useTranslations('moderator_actions');

  // Создаем действия модератора с переводами
  const getModeratorActions = () => [
    {
      title: t('chat_metrics.title'),
      label: t('chat_metrics.label'),
      action: 'Покажи метрики активности чатов за последние 7 дней',
    },
    {
      title: t('potential_leads.title'),
      label: t('potential_leads.label'),
      action:
        'Покажи список потенциальных лидов, отсортированных по дате создания',
    },
    {
      title: t('hot_leads.title'),
      label: t('hot_leads.label'),
      action: 'Найди горячие потенциальные лиды',
    },
    {
      title: t('recent_orders.title'),
      label: t('recent_orders.label'),
      action: 'Покажи последние 10 заказов со статусом новый',
    },
    {
      title: t('lead_details.title'),
      label: t('lead_details.label'),
      action: 'Покажи детальную информацию о либом лиде',
    },
    {
      title: t('create_lead.title'),
      label: t('create_lead.label'),
      action: 'Создай лид в CRM из заказа с ID 1',
    },
  ];

  const getActionsByModel = () => {
    switch (agentModel) {
      case AGENT_CHAT_MODELS.VISA_AGENT:
        return visaActions;
      case AGENT_CHAT_MODELS.VNJ_AGENT:
        return vnjActions;
      case AGENT_CHAT_MODELS.PMJ_AGENT:
        return pmjActions;
      case AGENT_CHAT_MODELS.RESIDENCE_AGENT:
        return residenceActions;
      case MODERATOR_CHAT_MODEL.id:
        return getModeratorActions();
      default:
        return visaActions;
    }
  };

  const suggestedActions = getActionsByModel();

  return (
    <div
      data-testid="suggested-actions"
      className="grid sm:grid-cols-2 gap-2 w-full overflow-y-auto max-h-[200px]"
    >
      {suggestedActions.map((suggestedAction, index) => (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 20 }}
          transition={{ delay: 0.05 * index }}
          key={`suggested-action-${suggestedAction.title}-${index}`}
          className="block"
        >
          <Button
            variant="ghost"
            onClick={async () => {
              await append({
                role: 'user',
                content: suggestedAction.action,
              });
            }}
            className="text-left border rounded-xl px-4 py-3.5 text-sm flex-1 gap-1 sm:flex-col w-full h-auto justify-start items-start"
          >
            <span className="font-medium truncate">
              {suggestedAction.title}
            </span>
            <span className="text-muted-foreground truncate max-w-full">
              {suggestedAction.label}
            </span>
          </Button>
        </motion.div>
      ))}
    </div>
  );
}

export const SuggestedActions = memo(PureSuggestedActions, () => true);
