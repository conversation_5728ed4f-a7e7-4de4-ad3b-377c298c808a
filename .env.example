# NextAuth Configuration
NEXTAUTH_URL=http://localhost:3000
AUTH_TRUST_HOST=true

# OpenAI API Key
OPENAI_API_KEY=****

# Authentication Secret (Generate using: openssl rand -base64 32)
AUTH_SECRET=****

# Vercel Blob Storage
BLOB_READ_WRITE_TOKEN=****

# Database Configuration
DATABASE_URL=****
DATABASE_URL_UNPOOLED=****

# PostgreSQL Configuration
PGDATABASE=neondb
PGHOST=****
PGHOST_UNPOOLED=****
PGPASSWORD=****
PGUSER=****

# Vercel Postgres Configuration
POSTGRES_DATABASE=neondb
POSTGRES_HOST=****
POSTGRES_PASSWORD=****
POSTGRES_PRISMA_URL=****
POSTGRES_URL=****
POSTGRES_URL_NON_POOLING=****
POSTGRES_URL_NO_SSL=****
POSTGRES_USER=****

#Cloudflare env configuration
CLOUDFLARE_ACCOUNT_ID=****
CLOUDFLARE_API_TOKEN=****
CLOUDFLARE_RAG_NAME=****

#MCP PARAMS
MCP_CONFIG_URL=****
MCP_CONF=****
MCP_CONFIG_PATH=****

#Locale
DEFAULT_LOCALE=****#only en/ru
#Feature flags
WITH_RAG=****#only true/false

#Google Configuration
GOOGLE_CLIENT_ID=****
GOOGLE_CLIENT_SECRET=****

#-----------------------------------------------
# Переменные для MCP Crawl4AI RAG клиента
#-----------------------------------------------

# URL для подключения к MCP серверу RAG (Retrieval Augmented Generation)
# Используется в lib/ai/tools/crawl4ai-rag/mcp-client.ts
MCP_RAG_SERVER_URL=http://localhost:8051/sse

#-----------------------------------------------
# Переменные для Bitrix24 интеграции
#-----------------------------------------------

# URL для подключения к MCP серверу Bitrix24
# Используется в lib/ai/tools/bitrix/get-leads.ts
MCP_SERVER_URL=https://remote-mcp-server-authless.alonalx.workers.dev/sse

# Домен Bitrix24 для интеграции
# Пример: b24-xxu92p.bitrix24.com
# Используется в lib/ai/tools/bitrix/get-leads.ts
BITRIX_DOMAIN=

# Токен вебхука Bitrix24 для авторизации
# Пример: 1/hyp4d13kixaq5r3k
# Используется в lib/ai/tools/bitrix/get-leads.ts
BITRIX_WEBHOOK_TOKEN=


AUTH_RESEND_KEY=abc

# GA4 параметры
# Путь до файла сервисного аккаунта Google Analytics
GCLOUD_CREDS_PATH=****
# Идентификатор для получения метрик
PROPERTIE_ID=****
# Идентификатор для отправки событий в Google Analytics
NEXT_PUBLIC_GA4_ID=****

#  Apple key generation
APPLE_TEAM_ID=****
APPLE_CLIENT_ID=****
APPLE_KEY_ID=****
APPLE_PRIVATE_KEY=****
JWT_EXPIRES_IN=86400

APPLE_SECRET=****