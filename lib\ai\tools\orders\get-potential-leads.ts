import { getAllOrders } from '@/lib/db/queries';
import { z } from 'zod';
import { tool } from 'ai';
import type { Session } from 'next-auth';
import type { DataStreamWriter } from 'ai';

interface GetPotentialLeadsProps {
  session: Session;
  dataStream: DataStreamWriter;
}

const GetPotentialLeadsSchema = z.object({
  limit: z.number().min(1).max(100).optional().default(50),
  offset: z.number().min(0).optional().default(0),
  filters: z
    .object({
      status: z.enum(['new', 'approved']).optional(),
      dateFrom: z.string().optional(),
      dateTo: z.string().optional(),
      recipientEmail: z.string().optional(),
      recipientName: z.string().optional(),
      recipientSecondName: z.string().optional(),
      recipientLastName: z.string().optional(),
      recipientPhone: z.string().optional(),
    })
    .optional()
    .default({}),
  sortBy: z
    .enum(['dateCreated', 'dateUpdated', 'status', 'id'], {
      errorMap: (issue, ctx) => {
        if (issue.code === 'invalid_enum_value') {
          return {
            message: `Сортировка по полю "${ctx.data}" не поддерживается. Доступные поля для сортировки: dateCreated, dateUpdated, status, id. Сортировка по полям получателя (recipientName, recipientEmail, recipientPhone) не поддерживается.`,
          };
        }
        return { message: ctx.defaultError };
      },
    })
    .optional()
    .default('dateCreated'),
  sortOrder: z.enum(['asc', 'desc']).optional().default('desc'),
});

export type GetPotentialLeadsInput = z.infer<typeof GetPotentialLeadsSchema>;

export const getPotentialLeads = ({
  session,
  dataStream,
}: GetPotentialLeadsProps) =>
  tool({
    description:
      'Получить список/таблицу потенциальных лидов с возможностью фильтрации по статусу, датам и контактным данным получателя. ВАЖНО: Доступные поля для сортировки ТОЛЬКО: dateCreated, dateUpdated, status, id. Сортировка по другим полям (например, recipientName, recipientEmail, recipientPhone) НЕ ПОДДЕРЖИВАЕТСЯ. Если пользователь просит сортировку по неподдерживаемому полю, объясни ограничения и предложи альтернативы.',
    parameters: GetPotentialLeadsSchema,
    execute: async (input) => {
      try {
        console.log(
          `[AI Tool] Requested 'getPotentialLeads' with input:`,
          input,
        );

        // Валидируем входные данные с детальной обработкой ошибок
        let validatedInput: GetPotentialLeadsInput;
        try {
          validatedInput = GetPotentialLeadsSchema.parse(input);
        } catch (validationError) {
          console.error('[AI Tool] Validation error:', validationError);
          return {
            success: false,
            error: `Ошибка валидации параметров: ${validationError instanceof Error ? validationError.message : 'Неизвестная ошибка валидации'}`,
            data: [],
            count_all: 0,
          };
        }

        console.log(
          `[AI Tool] Requested 'getPotentialLeads' with validated input:`,
          validatedInput,
        );

        // Дополнительная проверка sortBy
        const allowedSortFields = [
          'dateCreated',
          'dateUpdated',
          'status',
          'id',
        ];
        if (!allowedSortFields.includes(validatedInput.sortBy)) {
          return {
            success: false,
            error: `Поле для сортировки '${validatedInput.sortBy}' не поддерживается. Доступные поля: ${allowedSortFields.join(', ')}`,
            data: [],
            count_all: 0,
          };
        }

        // Преобразуем строковые даты в объекты Date, если они есть
        const processedFilters: any = { ...validatedInput.filters };
        if (processedFilters.dateFrom) {
          try {
            processedFilters.dateFrom = new Date(processedFilters.dateFrom);
            if (Number.isNaN(processedFilters.dateFrom.getTime())) {
              throw new Error('Неверный формат даты dateFrom');
            }
          } catch (dateError) {
            return {
              success: false,
              error: `Ошибка в формате даты dateFrom: ${dateError instanceof Error ? dateError.message : 'Неверный формат'}`,
              data: [],
              count_all: 0,
            };
          }
        }
        if (processedFilters.dateTo) {
          try {
            processedFilters.dateTo = new Date(processedFilters.dateTo);
            if (Number.isNaN(processedFilters.dateTo.getTime())) {
              throw new Error('Неверный формат даты dateTo');
            }
          } catch (dateError) {
            return {
              success: false,
              error: `Ошибка в формате даты dateTo: ${dateError instanceof Error ? dateError.message : 'Неверный формат'}`,
              data: [],
              count_all: 0,
            };
          }
        }

        console.log(
          `[AI Tool] Requested 'getPotentialLeads' with processed filters:`,
          processedFilters,
        );

        // Получаем заказы с фильтрацией
        const result = await getAllOrders({
          limit: validatedInput.limit,
          offset: validatedInput.offset,
          filters: processedFilters,
          sortBy: validatedInput.sortBy,
          sortOrder: validatedInput.sortOrder,
        });

        console.log(
          `[AI Tool] Requested 'getPotentialLeads' with result:`,
          result,
        );

        // Преобразуем заказы в потенциальные лиды
        const potentialLeads = result.orders.map((order) => ({
          id: order.id,
          status: order.status,
          recipient: order.recipient,
          dateCreated: order.dateCreated,
          dateUpdated: order.dateUpdated,
          // Дополнительные поля для анализа лида
          leadScore: calculateLeadScore(order),
          leadType: determineLeadType(order),
        }));

        const toolResult = {
          success: true,
          data: potentialLeads,
          count_all: result.count_all,
          pagination: result.pagination,
          sortOrder: validatedInput.sortOrder,
          sortBy: validatedInput.sortBy,
          filters: validatedInput.filters,
        };

        console.log(
          `[AI Tool] 'getPotentialLeads' successful. Found ${potentialLeads.length} leads`,
        );
        return toolResult;
      } catch (error) {
        console.error('[AI Tool] Failed to get potential leads:', error);
        const toolResult = {
          success: false,
          error:
            error instanceof Error ? error.message : 'Unknown error occurred',
          data: [],
          count_all: 0,
        };
        return toolResult;
      }
    },
  });

// Функция для расчета оценки лида (простая логика)
function calculateLeadScore(order: any): number {
  let score = 50; // базовая оценка

  // Увеличиваем оценку для новых заказов
  if (order.status === 'new') {
    score += 20;
  }

  // Увеличиваем оценку для недавних заказов
  const daysSinceCreated = Math.floor(
    (Date.now() - new Date(order.dateCreated).getTime()) /
      (1000 * 60 * 60 * 24),
  );
  if (daysSinceCreated <= 1) {
    score += 30;
  } else if (daysSinceCreated <= 7) {
    score += 15;
  }

  // Проверяем наличие полных контактных данных
  const recipient = order.recipient;
  if (recipient.email && recipient.phone) {
    score += 20;
  }
  if (recipient.name && recipient.lastName) {
    score += 10;
  }

  return Math.min(score, 100);
}

// Функция для определения типа лида
function determineLeadType(order: any): string {
  if (order.status === 'new') {
    const daysSinceCreated = Math.floor(
      (Date.now() - new Date(order.dateCreated).getTime()) /
        (1000 * 60 * 60 * 24),
    );

    if (daysSinceCreated <= 1) {
      return 'hot';
    } else if (daysSinceCreated <= 7) {
      return 'warm';
    } else {
      return 'cold';
    }
  }

  return 'converted';
}
