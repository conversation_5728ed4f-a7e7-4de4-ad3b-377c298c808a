# Техническая документация: Про<PERSON><PERSON><PERSON>с Авторизации

## 1. Введение
Данный документ описывает процесс авторизации в системе, включая клиентскую и серверную часть, обработку состояний и основные компоненты страницы входа.

## 2. Страница Логина

### 2.1 Основные компоненты
- **URL:** `/login`
- **Метод:** `POST`
- **Ключевые элементы интерфейса:**
    - Поле ввода `email`
    - Поле ввода `password`
    - Кнопка `Sign in`
    - Ссылка на страницу регистрации `/register`

### 2.2 Обработка состояний

| Состояние       | Действие                                      |
|-----------------|----------------------------------------------|
| `idle`         | Отображение формы входа                      |
| `failed`       | Вывод ошибки `Invalid credentials!`           |
| `invalid_data` | Вывод ошибки `Failed validating submission!`  |
| `success`      | Перенаправление после успешной авторизации    |

## 3. Технические детали реализации

### 3.1 Клиентская часть

#### **Технологии:**
- React
- Next.js
- TypeScript

#### **Основные функции:**
- `useActionState` — управление состоянием формы
- `useEffect` — обработка ответов сервера
- `toast` — отображение уведомлений об ошибках
- `useRouter` — навигация и обновление страницы

#### **Использование `useEffect`**
`useEffect` отслеживает состояние формы (`state.status`) и выполняет следующие действия:
- При `failed` или `invalid_data` отображает соответствующее уведомление через `toast`
- При `success`:
    - Устанавливает `isSuccessful` в `true`
    - Обновляет страницу через `router.refresh()`

### 3.2 Серверная часть

#### **Обработчик:** `../actions (функция login)`
#### **Механизм работы:**
1. Получение данных формы через `FormData`
2. Вызов функции `login` с переданными данными
3. Обработка результата:
    - При успехе: обновление страницы (`router.refresh()`)
    - При ошибке: отображение соответствующего сообщения

## 4. Пример использования

### 4.1 HTML-структура страницы входа
```html
<div className="w-full max-w-md">
  <h3>Sign In</h3>
  <p>Use your email and password to sign in</p>
  <AuthForm>
    <SubmitButton>Sign in</SubmitButton>
    <p>
      Don't have an account?
      <Link href="/register">Sign up</Link> for free.
    </p>
  </AuthForm>
</div>
```

### 4.2 Обработчик формы (`handleSubmit`)
```tsx
const handleSubmit = (formData: FormData) => {
  setEmail(formData.get('email') as string);
  formAction(formData);
};
```

## 5. Особенности реализации
- **Сохранение email между попытками входа:**
    - Используется `useState(email)`, передаваемый в `AuthForm`.
- **Обновление страницы после авторизации:**
    - Используется `router.refresh()`, а не `window.location.href`.
- **Навигация:**
    - При успешной авторизации страница обновляется
    - Предоставлена ссылка на страницу регистрации `/register`

