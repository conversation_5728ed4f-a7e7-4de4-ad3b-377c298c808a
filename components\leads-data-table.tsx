'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';

// Компонент для отображения данных лидов из Bitrix24
interface LeadsDataTableProps {
  leads: Array<any>;
  showAmount?: boolean;
  showExpandButton?: boolean;
}

export function LeadsDataTable({
  leads,
  showAmount = true,
  showExpandButton = true,
}: LeadsDataTableProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const translate_message = useTranslations('message');

  // Определяем заголовки таблицы на основе первого элемента
  const tableHeaders = leads.length > 0 ? Object.keys(leads[0]) : [];

  // Выбираем основные поля для отображения в предпросмотре
  const previewFields = ['ID', 'NAME', 'LAST_NAME', 'SECOND_NAME'];

  return (
    <div className="w-full">
      <div className="flex flex-col gap-2">
        <div className="flex items-center justify-between">
          <div className="text-sm font-medium">
            {showAmount && (
              <span className="text-gray-500">
                {translate_message('leads_found', {
                  count: leads.length.toString(),
                })}
              </span>
            )}
          </div>
          {showExpandButton && (
            <button
              type="button"
              onClick={() => setIsModalOpen(true)}
              className="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
            >
              {translate_message('show_table')}
            </button>
          )}
        </div>

        {/* Предпросмотр данных */}
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                {previewFields.map((header) => (
                  <th
                    key={header}
                    className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    {header}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {leads.slice(0, 5).map((lead) => (
                <tr key={lead.ID}>
                  {previewFields.map((field) => (
                    <td
                      key={field}
                      className="px-3 py-2 whitespace-nowrap text-sm text-gray-500"
                    >
                      {lead[field] !== null ? lead[field] : '-'}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Модальное окно с полной таблицей */}
      {isModalOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] flex flex-col">
            <div className="p-4 border-b flex justify-between items-center">
              <h2 className="text-lg font-semibold">
                {translate_message('leads_data_title')}
              </h2>
              <button
                type="button"
                onClick={() => setIsModalOpen(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                ✕
              </button>
            </div>
            <div className="p-4 overflow-auto flex-grow">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50 sticky top-0">
                  <tr>
                    {tableHeaders.map((header) => (
                      <th
                        key={header}
                        className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        {header}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {leads.map((lead) => (
                    <tr key={lead.ID}>
                      {tableHeaders.map((header) => (
                        <td
                          key={header}
                          className="px-3 py-2 whitespace-nowrap text-sm text-gray-500"
                        >
                          {lead[header] !== null ? lead[header] : '-'}
                        </td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            <div className="p-4 border-t flex justify-end">
              <button
                type="button"
                onClick={() => setIsModalOpen(false)}
                className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
              >
                {translate_message('close')}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
