// app/lib/apple.utils.ts

import { SignJWT } from "jose";
import { createPrivate<PERSON>ey } from "crypto";
import dotenv from "dotenv";
dotenv.config({path: ".env.local"});

// Явное объявление типа аргументов
type Args = {
  team_id?: string;
  iss?: string;
  private_key?: string;
  client_id?: string;
  sub?: string;
  key_id?: string;
  kid?: string;
  expires_in?: string;
  exp?: string;
};

const args = process.argv.slice(2).reduce<Args>((acc, arg, i, arr) => {
  if (arg.startsWith("--")) {
    const key = arg.replace(/^--/, "").toLowerCase() as keyof Args;
    acc[key] = arr[i + 1];
  }
  return acc;
}, {});

// Основные значения, подставляемые из args или env
const team_id = args.team_id ?? process.env.APPLE_TEAM_ID;
const iss = args.iss ?? team_id;

const private_key = args.private_key ?? process.env.APPLE_PRIVATE_KEY;
const client_id = args.client_id ?? process.env.APPLE_CLIENT_ID;
const sub = args.sub ?? client_id;

const key_id = args.key_id ?? process.env.APPLE_KEY_ID;
const kid = args.kid ?? key_id;

const expires_in = Number.parseInt(args.expires_in ?? process.env.JWT_EXPIRES_IN ?? "86400", 10);
const exp = args.exp ? Number.parseInt(args.exp, 10) : Math.floor(Date.now() / 1000) + expires_in;

console.log(key_id);

(async () => {
  const jwt = await new SignJWT({})
    .setAudience("https://appleid.apple.com")
    .setIssuer(<string>iss)
    .setSubject(<string>sub)
    .setIssuedAt()
    .setExpirationTime(exp)
    .setProtectedHeader({ alg: "ES256", kid })
    .sign(createPrivateKey(<string>private_key?.replace(/\\n/g, "\n")));

  console.log(`Apple client secret generated. Valid until: ${new Date(exp * 1000).toISOString()}`);
  console.log(jwt);
})();