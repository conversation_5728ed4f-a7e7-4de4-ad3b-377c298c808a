import { z } from 'zod';
import {
  getMcpClient,
  bitrixDomain,
  bitrixWebhookToken,
  callMcpTool,
  processMcpResponse,
} from './mcp-client';
import type { Session } from 'next-auth';
import { type DataStreamWriter, tool } from 'ai';
import { getOrderWithItems, updateOrderStatus } from '@/lib/db/queries';

interface CreateLeadProps {
  session: Session;
  dataStream: DataStreamWriter;
}

// Схема для одобрения потенциального лида
export const createLeadSchema = z.object({
  orderId: z
    .number()
    .describe('ID заказа для одобрения и создания лида в Bitrix24'),
  comments: z
    .string()
    .optional()
    .describe('Дополнительные комментарии к лиду (необязательное поле)'),
});

/**
 * Инструмент для одобрения потенциального лида и создания его в Bitrix24 CRM
 */
export const callMcpCreateLead = ({ session, dataStream }: CreateLeadProps) =>
  tool({
    description:
      'Одобрить потенциальный лид и создать его в Bitrix24 CRM. Инструмент автоматически получает данные заказа по ID, создает лид в Bitrix24 и изменяет статус заказа на "approved". Используется для конвертации потенциальных лидов в реальные лиды в CRM.',
    parameters: createLeadSchema,
    execute: async ({ orderId, comments }) => {
      console.log(`[AI Tool] Requested 'createLead' for order ID:`, orderId);

      try {
        // Получаем данные заказа из БД
        const order = await getOrderWithItems({ id: orderId });

        if (!order) {
          const errorMessage = `Заказ с ID ${orderId} не найден`;
          console.error(`[AI Tool] 'createLead' failed: ${errorMessage}`);

          if (dataStream) {
            dataStream.writeData({
              type: 'text',
              content: errorMessage,
            });
          }

          return {
            success: false,
            error: errorMessage,
          };
        }

        // Проверяем, что заказ еще не одобрен
        if (order.status === 'approved') {
          const errorMessage = `Заказ с ID ${orderId} уже одобрен`;
          console.error(`[AI Tool] 'createLead' failed: ${errorMessage}`);

          if (dataStream) {
            dataStream.writeData({
              type: 'text',
              content: errorMessage,
            });
          }

          return {
            success: false,
            error: errorMessage,
          };
        }

        const recipient = order.recipient;

        // Проверяем наличие всех обязательных полей
        const requiredFields = [
          { key: 'name', name: 'Имя контакта' },
          { key: 'secondName', name: 'Фамилия контакта' },
          { key: 'lastName', name: 'Отчество контакта' },
          { key: 'phone', name: 'Номер телефона' },
          { key: 'email', name: 'Email адрес' },
        ];

        const missingFields: string[] = [];

        for (const field of requiredFields) {
          const value = recipient[
            field.key as keyof typeof recipient
          ] as string;
          if (!value || value.trim() === '') {
            missingFields.push(field.name);
          }
        }

        if (missingFields.length > 0) {
          const errorMessage = `В заказе отсутствуют обязательные поля для создания лида: ${missingFields.join(', ')}`;
          console.error(
            `[AI Tool] 'createLead' validation failed: ${errorMessage}`,
          );

          if (dataStream) {
            dataStream.writeData({
              type: 'text',
              content: errorMessage,
            });
          }

          return {
            success: false,
            error: errorMessage,
          };
        }

        const totalAmount = order.items.reduce((sum: number, item: any) => {
          return sum + Number.parseFloat(item.price) * item.quantity;
        }, 0);

        // Подготавливаем данные для создания лида в Bitrix24
        const leadData = {
          NAME: recipient.name,
          LAST_NAME: recipient.lastName,
          SECOND_NAME: recipient.secondName || '',
          PHONE: [{ VALUE: recipient.phone, VALUE_TYPE: 'WORK' }],
          EMAIL: [{ VALUE: recipient.email, VALUE_TYPE: 'WORK' }],
          COMMENTS:
            comments ||
            recipient.comments ||
            `Лид создан из заказа #${orderId}`,
          SOURCE_ID: 'WEB', // Источник - веб-сайт
          STATUS_ID: 'NEW', // Статус - новый
          OPPORTUNITY: totalAmount.toFixed(2).toString(),
          CURRENCY_ID: order.items[0]?.currency || 'USD',
        };

        console.log(`[AI Tool] Creating lead in Bitrix24 with data:`, leadData);

        if (dataStream) {
          dataStream.writeData({
            type: 'text',
            content: `Создаю лид в Bitrix24 для заказа #${orderId}...`,
          });
        }

        // Создаем лид в Bitrix24
        const result = await _callMcpCreateLead(leadData);

        if (!result.success) {
          console.error(
            `[AI Tool] 'createLead' MCP call failed:`,
            result.error,
          );

          if (dataStream) {
            dataStream.writeData({
              type: 'text',
              content: `Ошибка при создании лида в Bitrix24: ${result.error}`,
            });
          }

          return {
            success: false,
            error: result.error,
          };
        }

        // Обновляем статус заказа на "approved"
        await updateOrderStatus({ id: orderId, status: 'approved' });

        const successMessage = `Лид успешно создан в Bitrix24 (ID: ${result.data}) и заказ #${orderId} одобрен`;
        console.log(
          `[AI Tool] 'createLead' completed successfully:`,
          successMessage,
        );

        if (dataStream) {
          dataStream.writeData({
            type: 'text',
            content: successMessage,
          });
        }

        return {
          success: true,
          data: {
            ID: result.data ? JSON.parse(result.data) : null,
            NAME: leadData.NAME,
            LAST_NAME: leadData.LAST_NAME,
            SECOND_NAME: leadData.SECOND_NAME,
            PHONE: leadData.PHONE,
            EMAIL: leadData.EMAIL,
          },
        };
      } catch (error) {
        const errorMessage = `Внутренняя ошибка при создании лида: ${error instanceof Error ? error.message : 'Неизвестная ошибка'}`;
        console.error(`[AI Tool] 'createLead' error:`, error);

        if (dataStream) {
          dataStream.writeData({
            type: 'text',
            content: errorMessage,
          });
        }

        return {
          success: false,
          error: errorMessage,
        };
      }
    },
  });

// Вызывает MCP инструмент для создания лида в Bitrix24
async function _callMcpCreateLead(leadData: any): Promise<{
  success: boolean;
  data?: string;
  error?: string;
}> {
  const mcpClient = getMcpClient();
  if (!mcpClient) {
    const errorMsg =
      'MCP client not configured (MCP_SERVER_URL environment variable is missing). Cannot call Bitrix24.';
    console.error(errorMsg);
    return { success: false, error: errorMsg };
  }
  try {
    const lead = JSON.stringify(leadData);
    const args: Record<string, unknown> = { lead };

    // Добавляем параметры Bitrix24, если они есть
    if (bitrixDomain && bitrixWebhookToken) {
      args.domain = bitrixDomain;
      args.token = bitrixWebhookToken;
    }

    console.log(
      `[MCP Client] Calling createLead with data and Bitrix domain: ${bitrixDomain || 'none'}`,
    );

    // Вызываем MCP инструмент через общую функцию из mcp-client
    const response: unknown = await callMcpTool('createLead', args);

    // Обрабатываем ответ через отдельную функцию
    return processMcpResponse(response);
  } catch (error: unknown) {
    console.error('[MCP Client] Error calling MCP createLead:', error);
    const message =
      error instanceof Error ? error.message : 'Unknown error during MCP call';
    return { success: false, error: `MCP Client Error: ${message}` };
  }
}
