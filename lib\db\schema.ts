import type { InferSelectModel } from 'drizzle-orm';
import {
  pgTable,
  varchar,
  timestamp,
  json,
  uuid,
  text,
  primaryKey,
  foreignKey,
  boolean,
  integer,
  uniqueIndex,
  pgEnum,
} from 'drizzle-orm/pg-core';
import { AGENT_CHAT_MODELS } from '@/lib/ai/models';
import type { AdapterAccountType } from 'next-auth/adapters';
import { UserRole } from '@/app/(auth)/auth.config';

export const userRole = pgEnum('userRole', [
  UserRole.USER,
  UserRole.ADMIN,
  UserRole.MODERATOR,
]);

export const user = pgTable('User', {
  id: uuid('id').primaryKey().notNull().defaultRandom(),
  name: text('name'),
  email: varchar('email', { length: 64 }).notNull(),
  emailVerified: timestamp('emailVerified', { mode: 'date' }),
  image: text('image'),
  password: varchar('password', { length: 64 }),
  role: varchar('role', typeof userRole)
    .notNull()
    .default(UserRole.USER),
});

export type User = InferSelectModel<typeof user>;

export const chat = pgTable(
  'Chat',
  {
    id: uuid('id').primaryKey().notNull().defaultRandom(),
    createdAt: timestamp('createdAt').notNull().defaultNow(),
    title: text('title').notNull(),
    agentModel: varchar('agentModel', { length: 255 })
      .notNull()
      .default(AGENT_CHAT_MODELS.VNJ_AGENT),
    userId: uuid('userId')
      .notNull()
      .references(() => user.id),
    visibility: varchar('visibility', { enum: ['public', 'private'] })
      .notNull()
      .default('private'),
  },
  (table) => ({
    userAgentIdx: uniqueIndex('unique_user_agent_chat').on(
      table.userId,
      table.agentModel,
    ),
  }),
);

export type Chat = InferSelectModel<typeof chat>;

// DEPRECATED: The following schema is deprecated and will be removed in the future.
// Read the migration guide at https://github.com/vercel/ai-chatbot/blob/main/docs/04-migrate-to-parts.md
export const messageDeprecated = pgTable('Message', {
  id: uuid('id').primaryKey().notNull().defaultRandom(),
  chatId: uuid('chatId')
    .notNull()
    .references(() => chat.id),
  role: varchar('role').notNull(),
  content: json('content').notNull(),
  createdAt: timestamp('createdAt').notNull(),
});

export type MessageDeprecated = InferSelectModel<typeof messageDeprecated>;

export const message = pgTable('Message_v2', {
  id: uuid('id').primaryKey().notNull().defaultRandom(),
  chatId: uuid('chatId')
    .notNull()
    .references(() => chat.id),
  role: varchar('role').notNull(),
  parts: json('parts').notNull(),
  attachments: json('attachments').notNull(),
  createdAt: timestamp('createdAt').notNull(),
});

export type DBMessage = InferSelectModel<typeof message>;

// DEPRECATED: The following schema is deprecated and will be removed in the future.
// Read the migration guide at https://github.com/vercel/ai-chatbot/blob/main/docs/04-migrate-to-parts.md
export const voteDeprecated = pgTable(
  'Vote',
  {
    chatId: uuid('chatId')
      .notNull()
      .references(() => chat.id),
    messageId: uuid('messageId')
      .notNull()
      .references(() => messageDeprecated.id),
    isUpvoted: boolean('isUpvoted').notNull(),
  },
  (table) => {
    return {
      pk: primaryKey({ columns: [table.chatId, table.messageId] }),
    };
  },
);

export type VoteDeprecated = InferSelectModel<typeof voteDeprecated>;

export const vote = pgTable(
  'Vote_v2',
  {
    chatId: uuid('chatId')
      .notNull()
      .references(() => chat.id),
    messageId: uuid('messageId')
      .notNull()
      .references(() => message.id),
    isUpvoted: boolean('isUpvoted').notNull(),
  },
  (table) => {
    return {
      pk: primaryKey({ columns: [table.chatId, table.messageId] }),
    };
  },
);

export type Vote = InferSelectModel<typeof vote>;

export const document = pgTable(
  'Document',
  {
    id: uuid('id').notNull().defaultRandom(),
    createdAt: timestamp('createdAt').notNull(),
    title: text('title').notNull(),
    content: text('content'),
    kind: varchar('text', { enum: ['text', 'code', 'image', 'sheet'] })
      .notNull()
      .default('text'),
    userId: uuid('userId')
      .notNull()
      .references(() => user.id),
  },
  (table) => {
    return {
      pk: primaryKey({ columns: [table.id, table.createdAt] }),
    };
  },
);

export type Document = InferSelectModel<typeof document>;

export const suggestion = pgTable(
  'Suggestion',
  {
    id: uuid('id').notNull().defaultRandom(),
    documentId: uuid('documentId').notNull(),
    documentCreatedAt: timestamp('documentCreatedAt').notNull(),
    originalText: text('originalText').notNull(),
    suggestedText: text('suggestedText').notNull(),
    description: text('description'),
    isResolved: boolean('isResolved').notNull().default(false),
    userId: uuid('userId')
      .notNull()
      .references(() => user.id),
    createdAt: timestamp('createdAt').notNull(),
  },
  (table) => ({
    pk: primaryKey({ columns: [table.id] }),
    documentRef: foreignKey({
      columns: [table.documentId, table.documentCreatedAt],
      foreignColumns: [document.id, document.createdAt],
    }),
  }),
);
export type Suggestion = InferSelectModel<typeof suggestion>;

export const account = pgTable(
  'Account',
  {
    userId: uuid('userid')
      .notNull()
      .references(() => user.id, { onDelete: 'cascade' }),
    type: text('type').$type<AdapterAccountType>().notNull(),
    provider: text('provider').notNull(),
    providerAccountId: text('providerAccountId').notNull(),
    refresh_token: text('refresh_token'),
    access_token: text('access_token'),
    expires_at: integer('expires_at'),
    token_type: text('token_type'),
    scope: text('scope'),
    id_token: text('id_token'),
    session_state: text('session_state'),
  },
  (table) => ({
    pk: primaryKey({ columns: [table.provider, table.providerAccountId] }),
  }),
);

export type Account = InferSelectModel<typeof account>;

export const sessions = pgTable('session', {
  sessionToken: text('sessiontoken').primaryKey(),
  userId: uuid('userid')
    .notNull()
    .references(() => user.id, { onDelete: 'cascade' }),
  expires: timestamp('expires', { mode: 'date' }).notNull(),
});

export const verificationTokens = pgTable(
  'verificationToken',
  {
    identifier: text('identifier').notNull(),
    token: text('token').notNull(),
    expires: timestamp('expires', { mode: 'date' }).notNull(),
  },
  (table) => ({
    pk: primaryKey({ columns: [table.identifier, table.token] }),
  }),
);

// Order management schemas
export const orderStatus = pgEnum('orderStatus', ['new', 'approved']);

export const services = pgTable('Services', {
  id: uuid('id').primaryKey().notNull().defaultRandom(),
  referenceId: varchar('reference_id', { length: 100 }).notNull(),
  title: text('title').notNull(),
  agentModel: text('agent_model'),
  category: text('category').notNull(),
  cost: varchar('cost', { length: 20 }).notNull(), // Using varchar for decimal precision
  currency: varchar('currency', { length: 3 }).notNull(),
  requiredPrepaid: boolean('required_prepaid').notNull().default(false),
  prepaidAmount: varchar('prepaid_amount', { length: 20 })
    .notNull()
    .default('0'), // Using varchar for decimal precision
});

export type Service = InferSelectModel<typeof services>;

export const orderItem = pgTable(
  'OrderItem',
  {
    id: uuid('id').primaryKey().notNull().defaultRandom(),
    orderId: integer('order_id').notNull(),
    serviceId: varchar('service_id', { length: 100 }).notNull(),
    title: text('title').notNull(),
    agentModel: text('agent_model').notNull(),
    quantity: integer('quantity').notNull(),
    price: varchar('price', { length: 20 }).notNull(), // Using varchar for decimal precision
    currency: varchar('currency', { length: 3 }).notNull(),
  },
  (table) => ({
    orderFk: foreignKey({
      columns: [table.orderId],
      foreignColumns: [order.id],
    }),
  }),
);

export type OrderItem = InferSelectModel<typeof orderItem>;

// OrderRecipient as Value Object (stored as JSON)
export interface OrderRecipient {
  name: string;
  secondName?: string;
  lastName: string;
  email: string;
  phone: string;
  comments?: string;
}

export const order = pgTable('Order', {
  id: integer('id').primaryKey().notNull().generatedAlwaysAsIdentity(),
  status: orderStatus('status').notNull().default('new'),
  recipient: json('recipient').$type<OrderRecipient>().notNull(),
  dateCreated: timestamp('date_created', { withTimezone: true })
    .notNull()
    .defaultNow(),
  dateUpdated: timestamp('date_updated', { withTimezone: true })
    .notNull()
    .defaultNow(),
});

export type Order = InferSelectModel<typeof order>;
