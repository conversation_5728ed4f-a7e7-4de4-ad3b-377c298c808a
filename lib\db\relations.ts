import { relations } from 'drizzle-orm/relations';
import {
  user,
  chat,
  message,
  messageDeprecated,
  suggestion,
  document,
  vote,
  voteDeprecated,
} from './schema';

export const chatRelations = relations(chat, ({ one, many }) => ({
  user: one(user, {
    fields: [chat.userId],
    references: [user.id],
  }),
  messageV2s: many(message),
  messages: many(messageDeprecated),
  voteV2s: many(vote),
  votes: many(voteDeprecated),
}));

export const userRelations = relations(user, ({ many }) => ({
  chats: many(chat),
  suggestions: many(suggestion),
  documents: many(document),
}));

export const messageV2Relations = relations(message, ({ one, many }) => ({
  chat: one(chat, {
    fields: [message.chatId],
    references: [chat.id],
  }),
  voteV2s: many(vote),
}));

export const messageRelations = relations(
  messageDeprecated,
  ({ one, many }) => ({
    chat: one(chat, {
      fields: [messageDeprecated.chatId],
      references: [chat.id],
    }),
    votes: many(voteDeprecated),
  }),
);

export const suggestionRelations = relations(suggestion, ({ one }) => ({
  user: one(user, {
    fields: [suggestion.userId],
    references: [user.id],
  }),
  document: one(document, {
    fields: [suggestion.documentId],
    references: [document.id],
  }),
}));

export const documentRelations = relations(document, ({ one, many }) => ({
  suggestions: many(suggestion),
  user: one(user, {
    fields: [document.userId],
    references: [user.id],
  }),
}));

export const voteV2Relations = relations(vote, ({ one }) => ({
  chat: one(chat, {
    fields: [vote.chatId],
    references: [chat.id],
  }),
  messageV2: one(message, {
    fields: [vote.messageId],
    references: [message.id],
  }),
}));

export const voteRelations = relations(voteDeprecated, ({ one }) => ({
  chat: one(chat, {
    fields: [voteDeprecated.chatId],
    references: [chat.id],
  }),
  message: one(messageDeprecated, {
    fields: [voteDeprecated.messageId],
    references: [messageDeprecated.id],
  }),
}));
