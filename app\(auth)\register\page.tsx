'use client';

import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { useActionState, useEffect, useState } from 'react';

import { AuthForm } from '@/components/auth-form';
import { SubmitButton } from '@/components/submit-button';

import { register, type RegisterActionState } from '../actions';
import { toast } from '@/components/toast';
import Image from 'next/image';
import { useTranslations } from 'next-intl';
import HorizontalLanguageSwitcher from '@/components/hor-language-switcher';
import { getAuthErrorMessageByCode } from '@/lib/utils';
import { motion } from 'framer-motion';
import { sendGAEvent } from '@next/third-parties/google';

export default function Page() {
  const searchParams = useSearchParams();
  const callbackUrl = searchParams.get('callbackUrl');
  const router = useRouter();

  const [email, setEmail] = useState('');
  const [isSuccessful, setIsSuccessful] = useState(false);
  const translation_registrate = useTranslations('register_page');

  const errorCode = searchParams.get('error');
  const errorMessage = getAuthErrorMessageByCode(errorCode);

  const [state, formAction] = useActionState<RegisterActionState, FormData>(
    register,
    {
      status: 'idle',
    },
  );

  useEffect(() => {
    if (state.status === 'user_exists') {
      toast({
        type: 'error',
        description: translation_registrate('error_user_exists'),
      });
    } else if (state.status === 'failed') {
      toast({
        type: 'error',
        description: translation_registrate('error_failed_create'),
      });
    } else if (state.status === 'invalid_data') {
      toast({
        type: 'error',
        description: translation_registrate('error_invalid_data'),
      });
    } else if (errorCode) {
      toast({
        type: 'error',
        description: errorMessage?.description,
      });
    } else if (state.status === 'success') {
      // Отправляем событие в Google Analytics
      sendGAEvent('event', 'user_registered', {
        user_email: email,
        timestamp: Date.now().toString(),
      });
      toast({
        type: 'success',
        description: translation_registrate('success_created'),
      });

      setIsSuccessful(true);
      router.refresh();
    }
  }, [state, router]);

  const handleSubmit = (formData: FormData) => {
    setEmail(formData.get('email') as string);
    formAction(formData);
  };

  const [offerAccepted, setOfferAccepted] = useState(false);

  const handleOfferChange = (value: boolean) => {
    setOfferAccepted(value);
  };

  return (
    <div
      className="flex flex-col md:flex-row min-h-dvh overflow-y-auto w-screen items-start pt-0 md:items-center justify-center bg-primary bg-[url('/images/Background.png')] bg-no-repeat bg-cover bg-center"
      style={{
        backgroundSize: '110% 98%',
      }}
    >
      {/* Языковой переключатель */}
      <div className="absolute top-4 right-4 z-10">
        <HorizontalLanguageSwitcher />
      </div>
      <div className="flex items-center justify-between self-center">
        <div className="flex flex-col items-center gap-2 text-white max-w-[300px]">
          <Image
            src="/images/Icon_user_login.png"
            alt="Agent image"
            width={300}
            height={48}
            className="flex flex-row items-center gap-2 ml-[50px] mr-[50px] max-w-[150px] md:max-w-[300px]"
            priority
          />
          <span className="flex flex-col italic text-sm md:text-xl">
            {translation_registrate('intro_message')}
          </span>
          <span className="flex flex-col font-bold italic text-xs md:text-md self-end pt-4">
            {translation_registrate('intro_author')}
          </span>
        </div>
      </div>
      <div className="w-full max-w-md overflow-hidden rounded-2xl flex flex-col gap-2 md:gap-12 pt-6 md:pt-0 self-center">
        <div className="flex flex-col items-center justify-center gap-2 px-4 text-center sm:px-16 text-white">
          <h3 className="text-md md:text-xl font-semibold">
            {translation_registrate('title')}
          </h3>
          <p className="text-xs md:text-sm">
            {translation_registrate('subtitle')}
          </p>
        </div>
        <motion.div
          className="bg-transparent"
          key="auth-form"
          initial={{ opacity: 0, scale: 0 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0, transition: { delay: 0.4 } }}
        >
          <AuthForm
            action={handleSubmit}
            defaultEmail={email}
            offerAccepted={offerAccepted}
            setOfferAccepted={handleOfferChange}
            isRegister
          >
            <SubmitButton isSuccessful={isSuccessful} disabled={!offerAccepted}>
              {translation_registrate('register_button')}
            </SubmitButton>
            <p className="text-center text-xs md:text-sm text-gray-300 mt-4">
              {translation_registrate('already_have_account')}
              <Link
                href={
                  callbackUrl
                    ? `/login?callbackUrl=${encodeURIComponent(callbackUrl)}`
                    : '/login'
                }
                className="font-semibold text-white hover:underline"
              >
                {translation_registrate('login_link')}
              </Link>
            </p>
          </AuthForm>
        </motion.div>
      </div>
    </div>
  );
}
