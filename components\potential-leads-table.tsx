'use client';

import { useRef, useState } from 'react';
import { useTranslations } from 'next-intl';
import { Button } from '@/components/ui/button';
import { AnimatePresence, motion } from 'framer-motion';
import useClickOutside from '@/hooks/use-click-outside';
import useSWR from 'swr';
import { ArrowLeftIcon, ArrowRightIcon, EyeIcon } from 'lucide-react';
import type { UseChatHelpers } from '@ai-sdk/react';
import { PlusIcon } from './icons';

const fetcher = (url: string) => fetch(url).then((res) => res.json());

interface PotentialLead {
  id: number;
  status: 'new' | 'approved';
  recipient: {
    name: string;
    secondName?: string;
    lastName: string;
    email: string;
    phone: string;
    comments?: string;
  };
  dateCreated: string;
  dateUpdated: string;
  leadScore: number;
  leadType: string;
}

interface PaginationInfo {
  limit: number;
  offset: number;
  total: number;
  hasMore: boolean;
  sortOrder: 'asc' | 'desc';
  sortBy: 'dateCreated' | 'dateUpdated' | 'status' | 'id';
  filters: Filters;
}

interface Filters {
  status?: 'new' | 'approved';
  dateFrom?: string;
  dateTo?: string;
  recipientEmail?: string;
  recipientName?: string;
  recipientPhone?: string;
}

interface PotentialLeadsTableProps {
  initialLeads?: PotentialLead[];
  initialCountAll?: number;
  initialPagination?: PaginationInfo;
  initialFilters?: Filters;
  initialSortBy?: 'dateCreated' | 'dateUpdated' | 'status' | 'id';
  initialSortOrder?: 'asc' | 'desc';
  append: UseChatHelpers['append'];
}

export function PotentialLeadsTable({
  initialLeads = [],
  initialCountAll = 0,
  initialPagination,
  initialFilters,
  initialSortBy,
  initialSortOrder,
  append,
}: PotentialLeadsTableProps) {
  const t = useTranslations('potential_leads.table');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(0);
  const [filters, setFilters] = useState<Filters>(initialFilters || {});
  const [sortBy, setSortBy] = useState<
    'dateCreated' | 'dateUpdated' | 'status' | 'id'
  >(initialSortBy || 'dateCreated');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>(
    initialSortOrder || 'desc',
  );
  const [limit, setLimit] = useState(initialPagination?.limit || 20); // Количество элементов на странице
  const offset = currentPage * limit;

  // Строим URL для SWR запроса
  const buildApiUrl = () => {
    const params = new URLSearchParams({
      limit: limit.toString(),
      offset: offset.toString(),
      sortBy,
      sortOrder,
    });

    // Добавляем фильтры
    Object.entries(filters).forEach(([key, value]) => {
      if (value) {
        params.append(key, value);
      }
    });

    return `/api/potential-leads?${params.toString()}`;
  };

  // SWR запрос для получения данных
  const { data, error, isLoading, mutate } = useSWR(buildApiUrl(), fetcher, {
    revalidateOnFocus: false,
    revalidateOnReconnect: false,
  });

  const getLeadTypeColor = (type: string) => {
    switch (type) {
      case 'hot':
        return 'bg-red-100 text-red-800';
      case 'warm':
        return 'bg-orange-100 text-orange-800';
      case 'cold':
        return 'bg-blue-100 text-blue-800';
      case 'converted':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600 font-bold';
    if (score >= 60) return 'text-orange-600 font-semibold';
    return 'text-red-600';
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ru-RU', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getFullName = (recipient: any) => {
    const parts = [
      recipient.lastName,
      recipient.name,
      recipient.secondName,
    ].filter(Boolean);
    return parts.length > 0 ? parts.join(' ') : t('not_specified');
  };

  // Основные поля для предпросмотра
  const previewFields = [t('created'), t('contact_info'), t('created')];

  const modalTableRef = useRef<HTMLDivElement>(null);

  useClickOutside(modalTableRef, (e) => {
    const target = e.target as HTMLElement;
    if (isModalOpen && target.nodeName !== 'BUTTON') setIsModalOpen(false);
  });

  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
  };

  const handleFilterChange = (key: keyof Filters, value: string) => {
    setFilters((prev) => ({
      ...prev,
      [key]: value || undefined,
    }));
    // delete data;
    setCurrentPage(0); // Сбрасываем на первую страницу при изменении фильтров
  };

  const handleLimitChange = (newLimit: number) => {
    setLimit(newLimit);
    setCurrentPage(0); // Сбрасываем на первую страницу при изменении лимита
  };

  const handleSortChange = (
    field: 'dateCreated' | 'dateUpdated' | 'status' | 'id',
  ) => {
    if (sortBy === field) {
      setSortOrder((prev) => (prev === 'asc' ? 'desc' : 'asc'));
    } else {
      setSortBy(field);
      setSortOrder('desc');
    }
    setCurrentPage(0); // Сбрасываем на первую страницу при изменении сортировки
  };

  const clearFilters = () => {
    setFilters({});
    setSortBy('dateCreated');
    setSortOrder('desc');
    setCurrentPage(0);
    setLimit(10); // Сбрасываем лимит на значение по умолчанию
  };

  return (
    <div className="w-full">
      {/* Элементы управления фильтрами и сортировкой */}

      {/* Обработка ошибок */}
      {error && (
        <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center gap-2 text-red-800">
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path
                fillRule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                clipRule="evenodd"
              />
            </svg>
            <span className="font-medium">{t('error_loading')}</span>
          </div>
          <p className="mt-1 text-sm text-red-700">
            {error.message || t('error_occurred')}
          </p>
        </div>
      )}

      {/* Состояние загрузки */}
      {isLoading && !isModalOpen && !data && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
          exit={{ opacity: 0 }}
          className="flex items-center justify-center py-8"
        >
          <div className="flex items-center gap-3 text-black">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500" />
            <span>{t('loading')}</span>
          </div>
        </motion.div>
      )}

      {/* Основной контент */}
      {!error && (
        <div className="flex flex-col gap-2">
          <div className="flex items-center justify-between">
            <div className="text-sm font-medium">
              <span className="text-gray-500">
                {t('potential_leads_found', {
                  count: data?.data?.length.toString(),
                  total: data?.pagination?.total.toString(),
                })}
              </span>
            </div>
            <button
              type="button"
              onClick={() => setIsModalOpen(true)}
              className="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
            >
              {t('show_table')}
            </button>
          </div>

          {/* Предпросмотр данных */}
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  {previewFields.map((header, index) => (
                    <th
                      key={`${header}-${index + 1}`}
                      className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      {header}
                    </th>
                  ))}
                  <th>&nbsp;</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {data?.data?.slice(0, 5).map((lead) => (
                  <tr key={lead.id}>
                    <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-900">
                      #{lead.id}
                    </td>
                    <td className="px-3 py-2 text-sm text-gray-500">
                      <div className="space-y-1">
                        <div>{getFullName(lead.recipient)}</div>
                        {lead.recipient.email && (
                          <div className="text-blue-600">
                            <a href={`mailto:${lead.recipient.email}`}>
                              {lead.recipient.email}
                            </a>
                          </div>
                        )}
                        {lead.recipient.phone && (
                          <div className="text-green-600">
                            <a href={`tel:${lead.recipient.phone}`}>
                              {lead.recipient.phone}
                            </a>
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                      {formatDate(lead.dateCreated)}
                    </td>
                    <td>
                      <Button
                        variant="ghost"
                        size={'sm'}
                        onClick={() =>
                          append({
                            role: 'user',
                            content: `aprove lead #${lead.id}`,
                          })
                        }
                      >
                        <PlusIcon size={16} />
                      </Button>

                      <Button
                        variant="ghost"
                        size={'sm'}
                        onClick={() =>
                          append({
                            role: 'user',
                            content: `get lead details #${lead.id}`,
                          })
                        }
                      >
                        <EyeIcon size={16} />
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>

            {/* Нет данных */}
            {!isLoading && !error && data && data.data.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                <svg
                  className="mx-auto h-12 w-12 text-gray-400"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
                <h3 className="mt-2 text-sm font-medium text-gray-900">
                  {t('no_leads')}
                </h3>
                <p className="mt-1 text-sm text-gray-500">
                  {t('no_leads_description')}
                </p>
              </div>
            )}
          </div>

          {/* Пагинация */}
          {data?.pagination && (
            <div className="flex items-center justify-between px-4 py-3 bg-white border-t border-gray-200">
              <div className="flex-1 flex justify-between sm:hidden">
                <Button
                  variant="default"
                  size="sm"
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 0}
                >
                  <ArrowLeftIcon size={16} />
                </Button>
                <select
                  id="limit-filter"
                  value={limit}
                  onChange={(e) =>
                    handleLimitChange(Number.parseInt(e.target.value))
                  }
                  className="w-1/5 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="1">1</option>
                  <option value="10">10</option>
                  <option value="20">20</option>
                  <option value="50">50</option>
                  <option value="100">100</option>
                </select>
                <Button
                  variant="default"
                  size="sm"
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={!data.pagination.hasMore}
                >
                  <ArrowRightIcon size={16} />
                </Button>
              </div>

              <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                  <p className="text-sm text-gray-700">
                    {t('showing')}{' '}
                    <span className="font-medium">
                      {currentPage * limit + 1}
                    </span>{' '}
                    -{' '}
                    <span className="font-medium">
                      {Math.min(
                        (currentPage + 1) * limit,
                        data.pagination.total,
                      )}
                    </span>{' '}
                    {t('of')}{' '}
                    <span className="font-medium">{data.pagination.total}</span>{' '}
                    {t('results')}
                  </p>
                  <select
                    id="limit-filter"
                    value={limit}
                    onChange={(e) =>
                      handleLimitChange(Number.parseInt(e.target.value))
                    }
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="1">1</option>
                    <option value="10">10</option>
                    <option value="20">20</option>
                    <option value="50">50</option>
                    <option value="100">100</option>
                  </select>
                </div>
                <div>
                  <nav className="relative z-0 gap-2 inline-flex rounded-md shadow-sm -space-x-px">
                    <Button
                      variant="default"
                      size="sm"
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage === 0}
                    >
                      <ArrowLeftIcon size={16} />
                    </Button>

                    <Button
                      variant="default"
                      size="sm"
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={!data.pagination.hasMore}
                    >
                      <ArrowRightIcon size={16} />
                    </Button>
                  </nav>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Модальное окно с полной таблицей */}
      <AnimatePresence>
        {isModalOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1, transition: { duration: 0.4 } }}
            exit={{ opacity: 0 }}
            key="modal-bg"
            className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
          >
            <motion.div
              ref={modalTableRef}
              initial={{ opacity: 0, scale: 0 }}
              animate={{
                opacity: 1,
                scale: 1,
                transition: { duration: 0.4 },
              }}
              exit={{ opacity: 0, scale: 0 }}
              key="modal"
              className="bg-white rounded-lg shadow-xl max-w-[calc(100%-200px)] w-full max-h-[90vh] flex flex-col"
            >
              {/* <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] flex flex-col"> */}
              <div className="p-4 border-b flex justify-between items-center">
                <h2 className="text-lg font-semibold">
                  {data?.pagination?.total
                    ? `${t('title')} (${data.pagination.total})`
                    : t('title')}
                </h2>
                <button
                  type="button"
                  onClick={() => setIsModalOpen(false)}
                  className="text-gray-500 hover:text-gray-700"
                >
                  ✕
                </button>
              </div>
              <div className="mb-4 p-4 bg-gray-50 rounded-lg">
                <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-4">
                  {/* Фильтр по статусу */}
                  <div>
                    <label
                      htmlFor="status-filter"
                      className="block text-sm font-medium text-gray-700 mb-1"
                    >
                      {t('status_label')}
                    </label>
                    <select
                      id="status-filter"
                      value={filters.status || ''}
                      onChange={(e) =>
                        handleFilterChange('status', e.target.value)
                      }
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="">{t('all_statuses')}</option>
                      <option value="new">{t('status_new')}</option>
                      <option value="approved">{t('status_approved')}</option>
                    </select>
                  </div>

                  {/* Фильтр по email */}
                  <div>
                    <label
                      htmlFor="email-filter"
                      className="block text-sm font-medium text-gray-700 mb-1"
                    >
                      {t('email_label')}
                    </label>
                    <input
                      id="email-filter"
                      type="text"
                      value={filters.recipientEmail || ''}
                      onChange={(e) =>
                        handleFilterChange('recipientEmail', e.target.value)
                      }
                      placeholder={t('search_email_placeholder')}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  {/* Фильтр по имени */}
                  <div>
                    <label
                      htmlFor="name-filter"
                      className="block text-sm font-medium text-gray-700 mb-1"
                    >
                      {t('name_label')}
                    </label>
                    <input
                      id="name-filter"
                      type="text"
                      value={filters.recipientName || ''}
                      onChange={(e) =>
                        handleFilterChange('recipientName', e.target.value)
                      }
                      placeholder={t('search_name_placeholder')}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  {/* Фильтр по телефону */}
                  <div>
                    <label
                      htmlFor="phone-filter"
                      className="block text-sm font-medium text-gray-700 mb-1"
                    >
                      {t('phone_label')}
                    </label>
                    <input
                      id="phone-filter"
                      type="text"
                      value={filters.recipientPhone || ''}
                      onChange={(e) =>
                        handleFilterChange('recipientPhone', e.target.value)
                      }
                      placeholder={t('search_phone_placeholder')}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>

                {/* Сортировка и действия */}
                <div className="flex flex-wrap items-center gap-4">
                  <div className="flex items-center gap-2">
                    <select
                      value={sortBy}
                      onChange={(e) =>
                        handleSortChange(
                          e.target.value as
                            | 'dateCreated'
                            | 'dateUpdated'
                            | 'status'
                            | 'id',
                        )
                      }
                      className="px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="dateCreated">
                        {t('sort_by_created')}
                      </option>
                      <option value="dateUpdated">
                        {t('sort_by_updated')}
                      </option>
                      <option value="status">{t('sort_by_status')}</option>
                      <option value="id">{t('sort_by_id')}</option>
                    </select>
                    <button
                      type="button"
                      onClick={() =>
                        setSortOrder((prev) =>
                          prev === 'asc' ? 'desc' : 'asc',
                        )
                      }
                      className="px-2 py-1 text-sm bg-gray-200 hover:bg-gray-300 rounded transition-colors"
                    >
                      {sortOrder === 'asc' ? '↑' : '↓'}
                    </button>
                  </div>

                  <button
                    type="button"
                    onClick={clearFilters}
                    className="px-3 py-1 text-sm bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
                  >
                    {t('reset_filters')}
                  </button>
                </div>
              </div>
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5 }}
                exit={{ opacity: 0 }}
                key="loading-data"
                className="p-4 overflow-auto flex-grow"
              >
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        ID
                      </th>
                      <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t('status_label')}
                      </th>
                      <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t('lead_type')}
                      </th>
                      <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t('contact_info')}
                      </th>
                      <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t('created')}
                      </th>
                      <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t('updated')}
                      </th>
                      <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t('comments')}
                      </th>
                      <th>&nbsp;</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {data?.data?.map((lead) => (
                      <tr key={lead.id}>
                        <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-900">
                          #{lead.id}
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap">
                          <span
                            className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              lead.status === 'new'
                                ? 'bg-blue-100 text-blue-800'
                                : 'bg-green-100 text-green-800'
                            }`}
                          >
                            {lead.status === 'new'
                              ? t('status_new')
                              : t('status_approved')}
                          </span>
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap">
                          <span
                            className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getLeadTypeColor(lead.leadType)}`}
                          >
                            {lead.leadType === 'hot'
                              ? t('lead_type_hot')
                              : lead.leadType === 'warm'
                                ? t('lead_type_warm')
                                : lead.leadType === 'cold'
                                  ? t('lead_type_cold')
                                  : t('lead_type_converted')}
                          </span>
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap text-sm">
                          <span className={getScoreColor(lead.leadScore)}>
                            {lead.leadScore}/100
                          </span>
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-900">
                          {getFullName(lead.recipient)}
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                          {lead.recipient.email ? (
                            <a
                              href={`mailto:${lead.recipient.email}`}
                              className="text-blue-600 hover:text-blue-900"
                            >
                              {lead.recipient.email}
                            </a>
                          ) : (
                            '-'
                          )}
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                          {lead.recipient.phone ? (
                            <a
                              href={`tel:${lead.recipient.phone}`}
                              className="text-green-600 hover:text-green-900"
                            >
                              {lead.recipient.phone}
                            </a>
                          ) : (
                            '-'
                          )}
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                          {formatDate(lead.dateCreated)}
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                          {formatDate(lead.dateUpdated)}
                        </td>
                        <td className="px-3 py-2 text-sm text-gray-500 max-w-xs truncate">
                          {lead.recipient.comments || '-'}
                        </td>
                        <td>
                          <Button
                            variant="ghost"
                            size={'sm'}
                            onClick={() =>
                              append({
                                role: 'user',
                                content: `aprove lead #${lead.id}`,
                              })
                            }
                          >
                            <PlusIcon size={16} />
                          </Button>

                          <Button
                            variant="ghost"
                            size={'sm'}
                            onClick={() => {
                              setIsModalOpen(false);
                              append({
                                role: 'user',
                                content: `get lead details of order #${lead.id}`,
                              });
                            }}
                          >
                            <EyeIcon size={16} />
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </motion.div>

              {/* Нет данных */}
              {!isLoading && !error && data && data.data.length === 0 && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.5 }}
                  exit={{ opacity: 0 }}
                  key="empty-data-indicator"
                  className="text-center py-8 text-gray-500"
                >
                  {/* <div className="text-center py-8 text-gray-500"> */}
                  <svg
                    className="mx-auto h-12 w-12 text-gray-400"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                    />
                  </svg>
                  <h3 className="mt-2 text-sm font-medium text-gray-900">
                    {t('no_leads')}
                  </h3>
                  <p className="mt-1 text-sm text-gray-500">
                    {t('no_leads_description')}
                  </p>
                </motion.div>
              )}
              {/* Индикатор загрузки при обновлении данных */}
              {isLoading && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.4 }}
                  exit={{ opacity: 0 }}
                  key="loading-indicator"
                  className="flex items-center justify-center py-2 bg-blue-50 rounded-lg"
                >
                  {/* <div className="flex items-center justify-center py-2 bg-blue-50 rounded-lg"> */}
                  <div className="flex items-center gap-2 text-sm text-blue-600">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500" />
                    <span>{t('loading')}</span>
                  </div>
                  {/* </div> */}
                </motion.div>
              )}
              {/* </AnimatePresence> */}
              {/* Пагинация */}
              {data?.pagination && (
                <div className="flex items-center justify-between px-4 py-3 bg-white border-t border-gray-200">
                  <div className="flex-1 flex gap-2 justify-between sm:hidden">
                    <Button
                      variant="default"
                      size="sm"
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage === 0}
                    >
                      <ArrowLeftIcon size={16} />
                    </Button>
                    {/* Выбор лимита записей на страницу */}
                    <div>
                      <select
                        id="limit-filter"
                        value={limit}
                        onChange={(e) =>
                          handleLimitChange(Number.parseInt(e.target.value))
                        }
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="1">1</option>
                        <option value="10">10</option>
                        <option value="20">20</option>
                        <option value="50">50</option>
                        <option value="100">100</option>
                      </select>
                    </div>
                    <Button
                      variant="default"
                      size="sm"
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={!data.pagination.hasMore}
                    >
                      <ArrowRightIcon size={16} />
                    </Button>
                  </div>
                  <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                      <p className="text-sm text-gray-700">
                        {data.pagination.total > 0 && (
                          <>
                            {t('showing')}{' '}
                            <span className="font-medium">
                              {currentPage * limit + 1}
                            </span>
                            {' - '}
                            <span className="font-medium">
                              {Math.min(
                                (currentPage + 1) * limit,
                                data.pagination.total,
                              )}
                            </span>{' '}
                            {t('of')}{' '}
                            <span className="font-medium">
                              {data.pagination.total}
                            </span>{' '}
                            {t('results')}
                          </>
                        )}
                      </p>
                    </div>
                    {/* Выбор лимита записей на страницу */}
                    <div>
                      <select
                        value={limit}
                        onChange={(e) =>
                          handleLimitChange(Number.parseInt(e.target.value))
                        }
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="1">1</option>
                        <option value="10">10</option>
                        <option value="20">20</option>
                        <option value="50">50</option>
                        <option value="100">100</option>
                      </select>
                    </div>
                    <div>
                      <nav className="relative z-0 gap-2 inline-flex rounded-md shadow-sm -space-x-px">
                        <Button
                          variant="default"
                          size="sm"
                          onClick={() => handlePageChange(currentPage - 1)}
                          disabled={currentPage === 0}
                        >
                          <ArrowLeftIcon size={16} />
                        </Button>
                        <Button
                          variant="default"
                          size="sm"
                          onClick={() => handlePageChange(currentPage + 1)}
                          disabled={!data.pagination.hasMore}
                        >
                          <ArrowRightIcon size={16} />
                        </Button>
                      </nav>
                    </div>
                  </div>
                </div>
              )}
              {/* </div> */}
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
