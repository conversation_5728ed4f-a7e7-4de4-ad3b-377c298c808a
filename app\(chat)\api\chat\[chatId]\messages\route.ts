import { type NextRequest, NextResponse } from 'next/server';
import { getMessagesByChatId } from '@/lib/db/queries';
import { auth } from '@/app/(auth)/auth';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ chatId: string }> }
) {
  try {
    const session = await auth();
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Проверяем роль пользователя - только модераторы и админы могут просматривать чаты
    if (session.user.role !== 'moderator' && session.user.role !== 'admin') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { chatId } = await params;
    
    if (!chatId) {
      return NextResponse.json({ error: 'Chat ID is required' }, { status: 400 });
    }

    const messages = await getMessagesByChatId({ id: chatId });
    
    return NextResponse.json({ 
      success: true, 
      messages: messages.map(msg => ({
        id: msg.id,
        role: msg.role,
        parts: msg.parts,
        createdAt: msg.createdAt,
      }))
    });
  } catch (error) {
    console.error('Failed to get chat messages:', error);
    return NextResponse.json(
      { error: 'Failed to get chat messages' },
      { status: 500 }
    );
  }
}
