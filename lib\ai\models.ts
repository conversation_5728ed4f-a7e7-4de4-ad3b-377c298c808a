import { UserRole } from '@/app/(auth)/auth.config';

export const DEFAULT_CHAT_MODEL: string = 'chat-model';

interface ChatModel {
  id: string;
  name: string;
  image: string;
  available: boolean;
  // description: string;
  // minPrice: number;
}

export enum AGENT_CHAT_MODELS {
  VISA_AGENT = 'chat-model-visa',
  VNJ_AGENT = 'chat-model-vnj',
  PMJ_AGENT = 'chat-model-pmj',
  RESIDENCE_AGENT = 'chat-model-residence',
  DIPLOM_AGENT = 'chat-model-diplom',
  BUSSINESS_AGENT = 'chat-model-bussiness',
  FREELANCE_AGENT = 'chat-model-freelance',
  MOVEMENT_AGENT = 'chat-model-movement',
}

export const MODERATOR_CHAT_MODEL: ChatModel = {
  id: UserRole.MODERATOR,
  name: 'agent_moderator',
  image: '',
  available: true,
};

export const agentChatModels: Array<ChatModel> = [
  {
    id: AGENT_CHAT_MODELS.VISA_AGENT,
    name: 'visa',
    image: '/images/P_1_1.png',
    available: true,
  },
  {
    id: AGENT_CHAT_MODELS.VNJ_AGENT,
    name: 'residence_permit',
    image: '/images/P_2_1.png',
    available: true,
  },
  {
    id: AGENT_CHAT_MODELS.PMJ_AGENT,
    name: 'permanent_residence',
    image: '/images/P_3_1.png',
    available: true,
  },
  {
    id: AGENT_CHAT_MODELS.RESIDENCE_AGENT,
    name: 'citizenship',
    image: '/images/P_4_1.png',
    available: true,
  },
  {
    id: AGENT_CHAT_MODELS.DIPLOM_AGENT,
    name: 'diploma_recognition',
    image: '/images/P_5_1.png',
    available: true,
  },
  {
    id: AGENT_CHAT_MODELS.BUSSINESS_AGENT,
    name: 'business',
    image: '/images/P_6_1.png',
    available: true,
  },
  {
    id: AGENT_CHAT_MODELS.FREELANCE_AGENT,
    name: 'freelance',
    image: '/images/P_7_1.png',
    available: true,
  },
  {
    id: AGENT_CHAT_MODELS.MOVEMENT_AGENT,
    name: 'relocation',
    image: '/images/P_8_1.png',
    available: true,
  },
];

// TODO: Remove after wirte tests
export const chatModels: Array<ChatModel> = [
  {
    id: 'chat-model-small',
    name: 'Small model',
    image: '/images/P_1_1.png',
    available: false,
  },
];
