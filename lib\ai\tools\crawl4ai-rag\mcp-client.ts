import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { SSEClientTransport } from '@modelcontextprotocol/sdk/client/sse.js';

// Задаем значения напрямую, если переменные окружения не доступны
const mcpServerUrl =
  process.env.MCP_RAG_SERVER_URL || 'http://localhost:8051/sse';

console.log(`[DEBUG RAG] MCP_RAG_SERVER_URL: ${mcpServerUrl}`);

if (!mcpServerUrl) {
  console.warn(
    'MCP_RAG_SERVER_URL environment variable is not set. RAG integration will be disabled.',
  );
}

export let mcpClient: Client | null = null;

export const initMcpClient = async () => {
  // Инициализация MCP клиента
  if (mcpServerUrl) {
    try {
      // Создаем клиент с параметрами
      mcpClient = new Client({
        name: 'agent-mcp-rag-client',
        version: '1.0.0',
      });

      console.log(
        '[MCP RAG Client] Initialized client with name: agent-mcp-rag-client',
      );

      // Создаем SSE транспорт для подключения к MCP серверу
      const serverUrl = new URL(mcpServerUrl);
      const transport = new SSEClientTransport(serverUrl);

      // Подключаемся к MCP серверу с использованием транспорта
      await mcpClient.connect(transport).catch(async (error) => {
        console.error(`[MCP RAG Client] Failed to connect: ${error}`);
        await closeMcpClient();
      });
    } catch (error) {
      console.error(`[MCP RAG Client] Failed to initialize: ${error}`);
      await closeMcpClient();

      mcpClient = null;
    }
  }
};

export const closeMcpClient = async () => {
  console.log('[MCP RAG Client] Closing MCP client...');
  if (mcpClient) {
    await mcpClient.close();
    mcpClient = null;
  }
};

// Убираем автоматическую инициализацию при импорте
// Инициализация будет происходить только при первом использовании performRagQuery
// if (!mcpClient) {
//   initMcpClient();
// }
