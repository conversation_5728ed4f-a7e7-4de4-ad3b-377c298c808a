// lib/rag/cloudflare/cfaisearch.types.ts

export enum AIModel {
    CONTEXT24K = `@cf/meta/llama-3.3-70b-instruct-fp8-fast`,
    CONTEXT60K = `@cf/meta/llama-3.1-8b-instruct-fast`,
    CONTEXT32K = `@cf/meta/llama-3.1-8b-instruct-fp8`,
}

export interface CloudflareSettings {
    accountId:string;
    autoragName:string;
    apiToken:string;
}

export interface AiSearchRequest {
    query: string;
    model: string;
    rewrite_query: boolean;
    max_num_results: number;
    ranking_options?: {
        score_threshold: number;
    };
    stream?: boolean;
}

export interface AiSearchResponse {
    success: boolean;
    result: {
        object: string;
        search_query: string;
        response: string;
        data: AiSearchResultItem[];
        has_more: boolean;
        next_page: string | null;
    };
}

export interface AiSearchResultItem {
    file_id: string;
    filename: string;
    score: number;
    attributes: Record<string, any>;
    content: AiSearchContent[];
}

export interface AiSearchContent {
    id: string;
    type: 'text'; // можно расширить если будут другие типы
    text: string;
}
