import { auth } from '@/app/(auth)/auth';
import { getAllOrders } from '@/lib/db/queries';
import type { UserRole } from '@/app/(auth)/auth.config';
import type { NextRequest } from 'next/server';

export const maxDuration = 60;

// Функция для расчета оценки лида
function calculateLeadScore(order: any): number {
  let score = 0;

  // Базовая оценка по статусу
  if (order.status === 'approved') score += 40;
  else if (order.status === 'new') score += 20;

  // Оценка по времени создания (более новые заказы получают больше баллов)
  const daysSinceCreated = Math.floor(
    (Date.now() - new Date(order.dateCreated).getTime()) /
      (1000 * 60 * 60 * 24),
  );
  if (daysSinceCreated <= 1) score += 30;
  else if (daysSinceCreated <= 7) score += 20;
  else if (daysSinceCreated <= 30) score += 10;

  // Оценка по полноте контактных данных
  const recipient = order.recipient || {};
  if (recipient.email) score += 15;
  if (recipient.phone) score += 15;
  if (recipient.name) score += 10;
  if (recipient.lastName) score += 10;

  return Math.min(score, 100); // Максимум 100 баллов
}

// Функция для определения типа лида
function determineLeadType(score: number, status: string): string {
  if (status === 'approved') return 'converted';
  if (score >= 80) return 'hot';
  if (score >= 60) return 'warm';
  return 'cold';
}

export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session || !session.user || !session.user.id) {
      return new Response('Unauthorized', { status: 401 });
    }

    // Проверяем роль пользователя (только модераторы могут получать лиды)
    const userRole = session.user.role as UserRole;
    if (userRole !== 'moderator' && userRole !== 'admin') {
      return new Response('Forbidden', { status: 403 });
    }

    const { searchParams } = new URL(request.url);

    // Получаем параметры из URL
    const limit = Math.min(
      Number.parseInt(searchParams.get('limit') || '50'),
      100,
    );
    const offset = Math.max(
      Number.parseInt(searchParams.get('offset') || '0'),
      0,
    );
    const sortBy = searchParams.get('sortBy') || 'dateCreated';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    // Получаем фильтры
    const filters: any = {};

    if (searchParams.get('status')) {
      filters.status = searchParams.get('status');
    }

    if (searchParams.get('dateFrom')) {
      filters.dateFrom = searchParams.get('dateFrom');
    }

    if (searchParams.get('dateTo')) {
      filters.dateTo = searchParams.get('dateTo');
    }

    if (searchParams.get('recipientEmail')) {
      filters.recipientEmail = searchParams.get('recipientEmail');
    }

    if (searchParams.get('recipientName')) {
      filters.recipientName = searchParams.get('recipientName');
    }

    if (searchParams.get('recipientPhone')) {
      filters.recipientPhone = searchParams.get('recipientPhone');
    }

    // Валидируем sortBy
    const allowedSortFields = ['dateCreated', 'dateUpdated', 'status', 'id'];
    if (!allowedSortFields.includes(sortBy)) {
      return new Response(
        JSON.stringify({
          success: false,
          error: `Сортировка по полю "${sortBy}" не поддерживается. Доступные поля: ${allowedSortFields.join(', ')}`,
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        },
      );
    }

    // Получаем заказы из базы данных
    const result = await getAllOrders({
      limit,
      offset,
      filters,
      sortBy: sortBy as 'dateCreated' | 'dateUpdated' | 'status' | 'id',
      sortOrder: sortOrder as 'asc' | 'desc',
    });

    // Преобразуем заказы в потенциальные лиды
    const leads = result.orders.map((order: any) => {
      const leadScore = calculateLeadScore(order);
      const leadType = determineLeadType(leadScore, order.status);

      return {
        id: order.id,
        status: order.status,
        recipient: order.recipient || {},
        dateCreated: order.dateCreated,
        dateUpdated: order.dateUpdated,
        leadScore,
        leadType,
      };
    });

    const response = {
      success: true,
      data: leads,
      pagination: {
        limit,
        offset,
        total: result.count_all || 0,
        hasMore: offset + limit < (result.count_all || 0),
      },
      count_all: result.count_all,
      sortOrder,
      sortBy,
      filters,
    };

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error('[API Potential Leads] Error:', error);
    return new Response(
      JSON.stringify({
        success: false,
        error: 'Внутренняя ошибка сервера',
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      },
    );
  }
}
