//./lib/mcp/adapter.ts
import { z } from 'zod';
import { type tool, jsonSchema, type ToolExecutionOptions } from 'ai';
import { MCPService } from './service';
import { loadMcpConfig, getMCPConfigList } from './load.config';

const ContentItemSchema = z.object({
  type: z.string(),
  text: z.string().optional(),
});

type ContentItem = z.infer<typeof ContentItemSchema>;

async function createExecuteFunction(
  mcpService: MCPService,
  clientId: string,
  toolName: string
): Promise<(args: unknown, _options: ToolExecutionOptions) => Promise<string>> {
  return async (args: unknown, _options: ToolExecutionOptions): Promise<string> => {
    const params = args as Record<string, any>;
    console.log("Calling tool:", toolName, "with params:", params);
    const response = await mcpService.callTool(clientId, toolName, params);

    if (response.isError) {
      const errorMessage = Array.isArray(response.content)
        ? (response.content[0] as { text?: string }).text
        : undefined;

      throw new Error(errorMessage || 'Ошибка при вызове инструмента');
    }

    let contentArray: ContentItem[] = [];

    if (Array.isArray(response.content)) {
      contentArray = response.content as ContentItem[];
    } else {
      throw new Error('Некорректный формат содержимого ответа от инструмента');
    }

    const resultText = contentArray.find(item => item.type === 'text')?.text;
    return resultText || 'Инструмент не вернул текстовый результат.';
  };
}

async function adaptToolsForClient(mcpService: MCPService, clientId: string) {
  const toolsList = await mcpService.listTools(clientId);
  const adaptedTools: Record<string, ReturnType<typeof tool>> = {};

  for (const toolInfo of toolsList.tools) {
    const { name, description, inputSchema } = toolInfo;
    const parametersSchema = jsonSchema(inputSchema);
    const executeFunction = await createExecuteFunction(mcpService, clientId, name);
    console.log(executeFunction);
    adaptedTools[name] = {
      description,
      parameters: parametersSchema,
      execute: executeFunction,
    };
  }
  return adaptedTools;
}

interface NextJSTools {
  allTools: Record<string, ReturnType<typeof tool>>;
  toolList: string[];
}
export async function initializeAllTools(mcp?: MCPService): Promise<NextJSTools> {
  const config = await loadMcpConfig();
  const allTools: Record<string, ReturnType<typeof tool>> = {};
  if (!config) {
    console.error('Failed to load MCP configuration');
    return {allTools, toolList: []}; // or throw new Error(...) depending on desired behavior
  }
  const clientConfigs = getMCPConfigList(config);
  if (!clientConfigs) {
    console.error('No client configurations found');
    return {allTools, toolList: []};
  }
  try {
    if(mcp){
        await mcp.connectAll();
        for (const clientConfig of clientConfigs) {
          const clientId = clientConfig.id;
          const clientTools = await adaptToolsForClient(mcp, clientId);
          Object.assign(allTools, clientTools);
      }
      return {allTools, toolList: Object.keys(allTools)};
    }
    const mcpService = new MCPService(clientConfigs);
    await mcpService.connectAll();
    for (const clientConfig of clientConfigs) {
      const clientId = clientConfig.id;
      const clientTools = await adaptToolsForClient(mcpService, clientId);
      Object.assign(allTools, clientTools);
    }
  } catch (error) {
      console.error('Ошибка при подключении к MCP:', error);
      return {allTools, toolList: []};
  }
return { allTools, toolList: Object.keys(allTools) };
}
