// lib/rag/vectorDB/rag.schema.ts
import {pgTable, serial, text, vector} from "drizzle-orm/pg-core";
import {InferSelectModel} from "drizzle-orm";

export const ragDocument = pgTable('knowledge', {
    id: serial('id').primaryKey(),
    fileName: text('file_name').notNull(),
    content: text('content').notNull(),
    embedding: vector('embedding', { dimensions: 384 }).notNull(), // Размерность должна соответствовать используемой модели
});
export type RagDocument = InferSelectModel<typeof ragDocument>;
