import { customProvider } from 'ai';
import { openai } from '@ai-sdk/openai';
import { AGENT_CHAT_MODELS, MODERATOR_CHAT_MODEL } from './models';

export const myProvider = customProvider({
  languageModels: {
    [AGENT_CHAT_MODELS.VISA_AGENT]: openai('gpt-4o-mini'),
    [AGENT_CHAT_MODELS.VNJ_AGENT]: openai('gpt-4.1-nano'),
    [AGENT_CHAT_MODELS.PMJ_AGENT]: openai('gpt-4o-mini'),
    [AGENT_CHAT_MODELS.RESIDENCE_AGENT]: openai('gpt-4o-mini'),
    [AGENT_CHAT_MODELS.DIPLOM_AGENT]: openai('gpt-4o-mini'),
    [AGENT_CHAT_MODELS.BUSSINESS_AGENT]: openai('gpt-4o-mini'),
    [AGENT_CHAT_MODELS.FREELANCE_AGENT]: openai('gpt-4o-mini'),
    [AGENT_CHAT_MODELS.MOVEMENT_AGENT]: openai('gpt-4o-mini'),
    [MODERATOR_CHAT_MODEL.id]: openai('gpt-4o-mini'),
  },
});
