'use client';

import { signIn } from 'next-auth/react';

export async function signInWithGoogle(callbackUrl: string | null) {
  try {
    const result = await signIn('google', {
      redirect: false,
      callbackUrl: callbackUrl || '/',
    });

    return result;
  } catch (error) {
    console.error('[Auth] Google sign in error:', error);
    throw error;
  }
}

export async function signInWithApple(callbackUrl: string | null) {
  try {
    const result = await signIn('apple', {
      redirect: false,
      callbackUrl: callbackUrl || '/',
    });

    return result;
  } catch (error) {
    console.error('[Auth] Apple sign in error:', error);
    throw error;
  }
}
