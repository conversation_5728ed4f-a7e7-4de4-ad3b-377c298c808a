{"not_available": "Not available", "agent_list": {"price_format": "from {price} EUR"}, "app_sidebar": {"sidebar_title": "BCA AI Agent", "add_agent": "Add agent"}, "auth_form": {"email_label": "Email Address", "password_label": "Password"}, "chat": {"add_attachment": "Add attachment", "send": "Send", "cancel": "Cancel", "submitting": "Submitting...", "generating_image": "Generating Image...", "initializing": "Initializing...", "loading_packages": "Loading packages...", "completed": "Completed", "failed": "Failed", "chat_placeholder_info": "Send a message...", "attention_info": "All messages generated in this chat using artificial intelligence technologies\nare intended for informational and educational purposes only.\nNo information provided in this chat should be considered legal advice\nor a substitute for consultation with a qualified lawyer."}, "chat_header": {"add_agent": "Add agent"}, "console": {"console": "<PERSON><PERSON><PERSON>", "close": "Close"}, "document": {"creating": "Creating", "created": "Created", "updating": "Updating", "updated": "Updated", "adding_suggestions": "Adding suggestions", "added_suggestions": "Added suggestions to"}, "document_preview": {"loading": "Loading..."}, "message": {"get_leads_title": "Getting leads list from Bitrix24", "with_filter": "with filter:", "create_lead_title": "Creating new lead in Bitrix24", "lead_created_success": "Lead successfully created in Bitrix24", "leads_found": "Found {count} lead(s)", "error_get_leads": "Error while getting leads...", "error_create_lead": "Error while creating lead...", "get_potential_leads_title": "Getting potential leads from orders", "error_get_potential_leads": "Error while getting potential leads...", "potential_leads_found": "Found {count} of {total} potential leads", "show_table": "Show table", "leads_data_title": "Leads data from Bitrix24", "close": "Close", "edit_message": "Edit message", "copy": "Copy", "upvote_response": "Upvote Response", "downvote_response": "Downvote Response", "reasoning": "Reasoning", "reasoned_for_seconds": "Reasoned for a few seconds", "assistant_message": "Hm...", "error_perform_rag_query": "Error while searching for information...", "result_perform_rag_query": "Found", "result_perform_rag_query_count": "results", "call_perform_rag_query": "Search info", "call_perform_rag_query_source": "at source"}, "message_actions": {"copy": "Copy", "upvote": "Upvote Response", "downvote": "Downvote Response"}, "message_editor": {"sending": "Sending...", "send": "Send", "cancel": "Cancel"}, "model_selector": {"select_model": "Select model"}, "multimodal_input": {"uploading": "Uploading...", "upload_file": "Upload file"}, "overview": {"default_description": "Our company Blue Card Agency will help you with such tasks as visa, residence permit, blue card, and citizenship in Germany. Choose the question you are interested in, and we will gladly consult you.", "moderator_welcome": "Welcome to the moderator panel! Here you can manage potential leads, analyze chat activity, approve orders, and create leads in the CRM system. Use the suggested actions below or ask your own question to work with the data."}, "preview_attachment": {"uploading": "Uploading..."}, "sidebar_history": {"delete_chat": "Delete", "delete_chat_confirmation": "Are you sure you want to delete this chat?", "delete_chat_success": "<PERSON><PERSON> deleted successfully", "delete_chat_error": "Failed to delete chat", "no_history_info": "Your conversations will appear here as soon as you start the chat!", "today": "Today", "yesterday": "Yesterday", "7day": "Last 7 days", "30day": "Last 30 days", "old": "Older"}, "sidebar_user_nav": {"toggle_mode": "Toggle {mode} mode", "sign_out": "Sign out"}, "sidebar_toggle": {"toggle_sidebar": "Open/close menu"}, "suggested_actions": {"visa": {"national_visa": "National Visa", "schengen": "Schengen Visa", "reunification": "Family Reunification Visa", "work": "Work Visa"}, "vnj": {"blue_card": "Blue Card", "freelancer": "Residence Permit for Freelancers", "study": "Student Residence Permit", "work": "Work Residence Permit"}, "pmj": {"conditions": "Conditions for PMJ", "duration": "Duration for PMJ", "documents": "Required Documents for PMJ", "language": "Language Requirements for PMJ"}, "residence": {"citizenship": "Citizenship", "conditions": "Conditions for Naturalization", "dual_citizenship": "Dual Citizenship with Germany", "test": "Citizenship Test"}}, "suggestion": {"apply": "Apply"}, "toolbar": {"summarize": "Summarize", "stop": "Stop"}, "version_footer": {"viewing_previous_version": "You are viewing a previous version", "restore_version": "Restore this version", "back_to_latest": "Back to latest version"}, "visibility_selector": {"private": "Private", "public": "Public", "only_you_can_access": "Only you can access this chat", "anyone_with_link": "Anyone with the link can access this chat"}, "toast": {"copy_success": "Copied to clipboard!", "copy_error": "There's no text to copy!", "upvote_loading": "Upvoting Response...", "upvote_success": "Upvoted Response!", "upvote_error": "Failed to upvote response.", "downvote_loading": "Downvoting Response...", "downvote_success": "Downvoted Response!", "downvote_error": "Failed to downvote response.", "delete_chat_loading": "Deleting chat...", "delete_chat_success": "<PERSON><PERSON> deleted successfully", "delete_chat_error": "Failed to delete chat"}, "service_headers": {"visa": "Visa", "residence_permit": "Residence Permit", "permanent_residence": "Permanent Residence", "citizenship": "Citizenship", "diploma_recognition": "Diploma Recognition", "business": "Business", "freelance": "Freelance", "relocation": "Relocation"}, "main_agent_labels": {"choose_label": "Choose a consultation topic", "visa": "Visa", "residence_permit": "Residence Permit", "permanent_residence": "Permanent Residence", "citizenship": "Citizenship", "diploma_recognition": "Diploma Recognition", "business": "Business", "freelance": "Freelance", "relocation": "Relocation", "no_translation": "No translation", "order_consultation": "Book a consultation", "with_expert": "with a BCA Expert", "agent_moderator": "Agent-Moderator"}, "alert_dialog": {"delete_confirm": "Continue", "delete_header": "Are you absolutely sure?", "delete_message": "This action cannot be undone. This will permanently delete your\nchat and remove it from our servers.", "cancel_message": "Cancel"}, "login_page": {"login_title": "Log in", "login_subtitle": "Enter your email and password", "login_button": "Log in", "intro_message": "Relocating to Germany can be fast and comfortable!", "intro_author": "Aleksei Varivoda", "no_account": "Don't have an account? ", "register_link": "Sign up", "register_suffix": " for free.", "error_invalid_credentials": "Invalid credentials!", "error_invalid_data": "Failed validating your submission!", "email": "Email", "password": "Password", "sign_in_with_google": "Sign in with Google", "magick_link_subtitle": "Enter your email and receive a sign-in link", "login_another_way": "Or sign in with:", "password_recovery": "Recover password", "back": "Back"}, "register_page": {"title": "Sign Up", "subtitle": "Create an account with your email and password", "register_button": "Sign Up", "already_have_account": "Already have an account? ", "login_link": "Log in", "error_user_exists": "An account with this email already exists!", "error_failed_create": "Failed to create account!", "error_invalid_data": "Failed validating your submission!", "success_created": "Account created successfully!", "email": "Email", "password": "Password", "intro_message": "Relocating to Germany can be fast and comfortable!", "intro_author": "Aleksei Varivoda"}, "agreements": {"label_agreements": "I have read and accept the", "link_agreements": "terms of the public offer"}, "auth": {"email_sent_title": "<PERSON><PERSON>", "email_sent_success_title": "Check Your Email", "email_sent_description": "We've sent a sign-in link to {email}", "email_sent_check_spam": "If you don't see the email, please check your spam folder", "back_to_login": "Back to Login", "back_to_home": "Back to Home", "email_template": {"sign_in_to": "You requested to sign in to the system. To do this, simply click the button below or open the link manually.", "sign_in_button": "Sign in to system", "ignore_message": "If you did not request to sign in — simply ignore this email. Your data is safe.", "custom_header": "Hello!", "link_expires": "The link is valid for 15 minutes and can only be used once.", "regards": "Best regards,", "team_signature": "BCA Team"}}, "auth_errors": {"title": "Authentication error", "back_to_login": "Back to login", "back_to_home": "Back to home", "unknown_error": {"title": "An unknown error occurred", "description": "Please try to log in again or contact the administrator."}, "credentials_signin": {"title": "Invalid credentials", "description": "Please check your username and password."}, "access_denied": {"title": "Access denied", "description": "You don't have permission to access the requested resource."}, "oauth_account_not_linked": {"title": "Account not linked", "description": "This email is already associated with another account. Please sign in using your previous method."}, "oauth_callback_error": {"title": "OAuth authorization error", "description": "An error occurred during authorization with an external service. Please try again."}, "oauth_signin_error": {"title": "<PERSON><PERSON>uth sign-in error", "description": "Failed to sign in with the selected provider. Please try another sign-in method."}, "email_signin_error": {"title": "Email sign-in error", "description": "Failed to send the sign-in email. Please check your email address."}, "verification": {"title": "Verification error", "description": "The sign-in link is invalid or has expired. Please request a new link."}, "webauthn_verification_error": {"title": "WebAuthn verification error", "description": "Failed to verify WebAuthn credentials. Please try another sign-in method."}, "account_not_linked": {"title": "Account not linked", "description": "This email is already associated with another account. Please sign in using your previous method."}, "missing_csrf": {"title": "CSRF security error", "description": "Missing CSRF protection. Please make sure your browser accepts cookies."}, "callback_route_error": {"title": "Callback error", "description": "An error occurred while processing the authentication request. Please try again."}, "session_token_error": {"title": "Session error", "description": "Failed to retrieve session data. Please try to log in again."}, "signout_error": {"title": "Sign-out error", "description": "An error occurred while trying to sign out. Please try again."}, "configuration_error": {"title": "System configuration error", "description": "An error occurred in the authentication system settings. Please contact the administrator."}}, "analytics_tool": {"processing": "Retrieving statistics and metrics...", "error": "An error occurred while retrieving the information. Please try again.", "finished": "The data has been successfully retrieved."}, "potential_leads": {"table": {"search_email_placeholder": "Search by email...", "search_name_placeholder": "Search by name...", "search_phone_placeholder": "Search by phone...", "not_specified": "Not specified", "error_loading": "Error loading data", "error_occurred": "An error occurred while loading potential leads", "loading": "Loading potential leads...", "no_leads": "No potential leads", "no_leads_description": "Try changing filters or waiting for new orders.", "showing": "Showing", "of": "of", "results": "results", "title": "Potential Leads", "status_label": "Status", "all_statuses": "All statuses", "status_new": "New", "status_approved": "Approved", "email_label": "Recipient Email", "name_label": "Recipient Name", "phone_label": "Recipient Phone", "sort_by_created": "By creation date", "sort_by_updated": "By update date", "sort_by_status": "By status", "sort_by_id": "By ID", "reset_filters": "Reset filters", "lead_type": "Lead Type", "contact_info": "Contact Info", "created": "Created", "approve_lead": "Approve Lead", "view_details": "View Details", "potential_leads_found": "Found {count} of {total} potential leads", "show_table": "Show Table", "lead_type_hot": "Hot", "lead_type_warm": "Warm", "lead_type_cold": "Cold", "lead_type_converted": "Converted", "updated": "Updated", "comments": "Comments"}, "details": {"title": "Lead Details", "order_info": "Order Information", "contact_info": "Contact Information", "services": "Services", "related_chats": "Related Chats", "created": "Created", "updated": "Updated", "status_new": "New", "status_approved": "Approved", "full_name": "Full Name", "phone": "Phone", "comments": "Comments", "services_count": "Services", "total_amount": "Total Amount", "ai_agents_count": "AI Agents", "loading_messages": "Loading messages...", "no_messages": "No messages in this chat", "view_chat": "View Chat", "not_available": "Not available"}}, "moderator_actions": {"chat_metrics": {"title": "Chat Metrics", "label": "Get chat activity statistics"}, "potential_leads": {"title": "Potential Leads", "label": "Show list of potential leads"}, "lead_details": {"title": "Lead Details", "label": "View detailed lead information"}, "create_lead": {"title": "Approve Lead", "label": "Approve potential lead and add to Bitrix24"}, "recent_orders": {"title": "Recent Leads", "label": "Show latest potential leads with 'new' status"}, "hot_leads": {"title": "Hot Leads", "label": "Find hot potential leads"}}}