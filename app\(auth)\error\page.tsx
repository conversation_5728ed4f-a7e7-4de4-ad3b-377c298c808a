'use client';

import { useSearchParams } from 'next/navigation';
import Link from 'next/link';
import HorizontalLanguageSwitcher from '@/components/hor-language-switcher';
import { getAuthErrorMessageByCode } from '@/lib/utils';
import { useTranslations } from 'next-intl';

export default function ErrorPage() {
  const searchParams = useSearchParams();
  const errorCode = searchParams.get('error');
  const errorMessage = getAuthErrorMessageByCode(errorCode);

  const t = useTranslations('auth_errors');

  return (
    <div
      className="flex flex-col md:flex-row min-h-dvh overflow-y-auto w-screen items-start pt-0 md:items-center justify-center bg-primary bg-[url('/images/Background.png')] bg-no-repeat bg-cover bg-center"
      style={{
        backgroundSize: '110% 98%',
      }}
    >
      {/* Языковой переключатель */}
      <div className="absolute top-4 right-4 z-10">
        <HorizontalLanguageSwitcher />
      </div>
      <div className="w-full max-w-md rounded-lg border border-gray-200 bg-white p-6 shadow-md dark:border-gray-700 dark:bg-gray-800">
        <h1 className="mb-4 text-xl font-bold text-red-600 dark:text-red-500">
          {t('title')}
        </h1>
        <div className="mb-4 rounded-md bg-red-50 p-4 dark:bg-red-900/20">
          <p className="text-sm font-medium text-gray-800 dark:text-gray-200">
            {errorMessage.title}
          </p>
          <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
            {errorMessage.description}
          </p>
        </div>
        <div className="flex justify-between">
          <Link
            href="/login"
            className="inline-flex items-center rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            {t('back_to_login')}
          </Link>
          <Link
            href="/"
            className="inline-flex items-center rounded-md bg-gray-100 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600"
          >
            {t('back_to_home')}
          </Link>
        </div>
      </div>
    </div>
  );
}
