'use client';

import type { Attachment, UIMessage } from 'ai';
import { useChat } from '@ai-sdk/react';
import { useEffect, useState } from 'react';
import useSWR, { useSWRConfig } from 'swr';
import { ChatHeader } from '@/components/chat-header';
import type { Vote } from '@/lib/db/schema';
import {
  fetcher,
  generateUUID,
  isTimeDifferenceGreaterThanDays,
} from '@/lib/utils';
import { Artifact } from './artifact';
import { MultimodalInput } from './multimodal-input';
import { Messages } from './messages';
import { useArtifactSelector } from '@/hooks/use-artifact';
import { toast } from 'sonner';
import { useTranslations } from 'next-intl';
import { MODERATOR_CHAT_MODEL } from '@/lib/ai/models';
import { sendGAEvent } from '@next/third-parties/google';

export function Chat({
  id,
  initialMessages,
  selectedChatModel,
  isReadonly,
  startAgent,
}: {
  id: string;
  initialMessages: Array<UIMessage>;
  selectedChatModel: string;
  isReadonly: boolean;
  startAgent?: boolean;
}) {
  const { mutate } = useSWRConfig();

  const {
    messages,
    setMessages,
    handleSubmit,
    input,
    setInput,
    append,
    status,
    stop,
    reload,
  } = useChat({
    id,
    body: { id, selectedChatModel: selectedChatModel },
    initialMessages,
    experimental_throttle: 100,
    sendExtraMessageFields: true,
    generateId: generateUUID,
    onFinish: () => {
      mutate('/api/history');
    },
    onError: (error) => {
      toast.error('An error occured, please try again!');
    },
  });

  const { data: votes } = useSWR<Array<Vote>>(
    messages.length >= 2 ? `/api/vote?chatId=${id}` : null,
    fetcher,
  );

  const translation_chat = useTranslations('chat');
  const [attachments, setAttachments] = useState<Array<Attachment>>([]);
  const isArtifactVisible = useArtifactSelector((state) => state.isVisible);

  const isReturned = isTimeDifferenceGreaterThanDays(
    messages[messages.length - 1]?.createdAt,
    1,
  );

  useEffect(() => {
    if (isReturned)
      sendGAEvent('event', 'return_user', { agent: selectedChatModel });
    if (startAgent && selectedChatModel !== MODERATOR_CHAT_MODEL.id) {
      sendGAEvent('event', 'create_agent', { agent: selectedChatModel });
      console.log('Starting agent...');
      // send initial message which will be not displayed, but saved in DB
      // TODO: make initial message for prompt
      append({
        role: 'user',
        content: ' ',
      });
    }
  }, [startAgent]);

  return (
    <>
      <div className="flex flex-col min-w-0 h-dvh bg-background">
        <ChatHeader
        // chatId={id}
        // selectedModelId={selectedChatModel}
        // selectedVisibilityType={selectedVisibilityType}
        // isReadonly={isReadonly}
        />

        <Messages
          chatId={id}
          status={status}
          votes={votes}
          // @ts-ignore
          messages={messages}
          setMessages={setMessages}
          reload={reload}
          append={append}
          isReadonly={isReadonly}
          isArtifactVisible={isArtifactVisible}
          selectedChatModel={selectedChatModel}
        />

        <form className="flex flex-col mx-auto px-4 bg-background pb-4 md:pb-6 gap-2 w-full md:max-w-3xl">
          {!isReadonly && (
            <MultimodalInput
              chatId={id}
              selectedChatModel={selectedChatModel}
              input={input}
              setInput={setInput}
              handleSubmit={handleSubmit}
              status={status}
              stop={stop}
              attachments={attachments}
              setAttachments={setAttachments}
              // @ts-ignore
              messages={messages}
              setMessages={setMessages}
              append={append}
            />
          )}

          <div className="flex text-foreground text-[8px]">
            {translation_chat('attention_info')}
          </div>
        </form>
      </div>

      <Artifact
        selectedChatModel={selectedChatModel}
        chatId={id}
        input={input}
        setInput={setInput}
        handleSubmit={handleSubmit}
        status={status}
        stop={stop}
        attachments={attachments}
        setAttachments={setAttachments}
        append={append}
        // @ts-ignore
        messages={messages}
        setMessages={setMessages}
        reload={reload}
        votes={votes}
        isReadonly={isReadonly}
      />
    </>
  );
}
