'use client';

import type { User } from 'next-auth';
import { useRouter } from 'next/navigation';

import { SidebarHistory } from '@/components/sidebar-history';
import { SidebarUserNav } from '@/components/sidebar-user-nav';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  useSidebar,
} from '@/components/ui/sidebar';
import Link from 'next/link';
import Image from 'next/image';

import { useTranslations } from 'next-intl';

export function AppSidebar({ user }: { user: User | undefined }) {
  const router = useRouter();
  const translation_sidebar = useTranslations('main_agent_labels');
  const { setOpenMobile } = useSidebar();

  return (
    <Sidebar className="group-data-[side=left]:border-r-0">
      <SidebarHeader>
        <SidebarMenu>
          <div className="flex flex-row justify-between items-center">
            <Link
              href="/"
              onClick={() => {
                setOpenMobile(false);
              }}
              className="flex flex-row gap-3 items-center"
            >
              <span className="flex flex-row items-center gap-2 cursor-pointer">
                <Image
                  src="/images/Logo.png"
                  alt="Logo"
                  width={240}
                  height={136}
                  className="inline-block"
                  priority
                />
              </span>
            </Link>
          </div>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <SidebarHistory user={user} />
      </SidebarContent>
      <Link
        href="/"
        className="flex flex-row items-center gap-2 text-sidebar-primary hover:text-sidebar-primary-foreground px-4 py-[15px]"
      >
        <Image
          src="/images/Button_expert_call.png"
          alt="Button_expert_call"
          width={50}
          height={48}
          className="flex flex-row items-center gap-2"
          priority
        />
        <div className="flex flex-col">
          <span className="flex text-xs">
            {translation_sidebar('order_consultation')}
          </span>
          <span className="flex text-xs font-bold">
            {translation_sidebar('with_expert')}
          </span>
        </div>
      </Link>
      <SidebarFooter>{user && <SidebarUserNav user={user} />}</SidebarFooter>
    </Sidebar>
  );
}
