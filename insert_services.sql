-- SQL скрипт для заполнения таблицы Services данными из CSV файлов
-- Сопоставление категорий из CSV с agent_model значениями:
-- "Признание диплома" -> "chat-model-diplom" (DIPLOM_AGENT)
-- "Переезд" -> "chat-model-movement" (MOVEMENT_AGENT)  
-- "Бизнес" -> "chat-model-bussiness" (BUSSINESS_AGENT)
-- "Фриланс" -> "chat-model-freelance" (FREELANCE_AGENT)
-- "Виза" -> "chat-model-visa" (VISA_AGENT)
-- "ВНЖ" -> "chat-model-vnj" (VNJ_AGENT)
-- "ПМЖ" -> "chat-model-pmj" (PMJ_AGENT)
-- "Гражданство" -> "chat-model-residence" (RESIDENCE_AGENT)

-- Услуги для признания диплома
INSERT INTO "Services" (reference_id, title, agent_model, category, cost, currency, required_prepaid, prepaid_amount) VALUES
('DIPLOM_001', 'Сбор и подача документов в Министерство здравоохранения федеральной земли', 'chat-model-diplom', 'Документы', '170', 'EUR', false, '0'),
('DIPLOM_002', 'Составление заявления для подачи документов на оценку образования', 'chat-model-diplom', 'Документы', '175', 'EUR', false, '0'),
('DIPLOM_003', 'Сбор и проверка документов по чек-листу для оценки образования', 'chat-model-diplom', 'Документы', '265', 'EUR', false, '0'),
('DIPLOM_004', 'Сбор документов для подтверждения медицинского диплома', 'chat-model-diplom', 'Документы', '1350', 'EUR', false, '0'),
('DIPLOM_005', 'Координация процесса признания диплома сотрудником ВСА', 'chat-model-diplom', 'Координация', '560', 'EUR', false, '0'),
('DIPLOM_006', 'Сопровождение и поддержка в Ärztekammer для получения записи на Fachsprachprüfung', 'chat-model-diplom', 'Сопровождение', '199', 'EUR', false, '0'),
('DIPLOM_007', 'Сопровождение и поддержка в оформлении и получении Certificate of Good Standing', 'chat-model-diplom', 'Сопровождение', '149', 'EUR', false, '0'),
('DIPLOM_008', 'Сопровождение и поддержка в оформлении и получении свидетельства об аккредитации', 'chat-model-diplom', 'Сопровождение', '150', 'EUR', false, '0'),
('DIPLOM_009', 'Сопровождение и поддержка в переводе и нотариальном удостоверении документов', 'chat-model-diplom', 'Сопровождение', '199', 'EUR', false, '0'),
('DIPLOM_010', 'Заверение копий документов (признание мед. диплома)', 'chat-model-diplom', 'Проверка и заверение', '250', 'EUR', false, '0'),
('DIPLOM_011', 'Запрос списка документов в ведомстве земли, где планируется подача', 'chat-model-diplom', 'Проверка и заверение', '99', 'EUR', false, '0'),
('DIPLOM_012', 'Проверка диплома в реестре АНАБИН', 'chat-model-diplom', 'Проверка и заверение', '85', 'EUR', false, '0'),
('DIPLOM_013', 'Проверка документов перед подачей в ZAB', 'chat-model-diplom', 'Проверка и заверение', '300', 'EUR', false, '0');

-- Услуги для переезда
INSERT INTO "Services" (reference_id, title, agent_model, category, cost, currency, required_prepaid, prepaid_amount) VALUES
('MOVEMENT_001', 'Заключение договора аренды жилья', 'chat-model-movement', 'Жилье', '770', 'EUR', false, '0'),
('MOVEMENT_002', 'Подбор трех вариантов жилья', 'chat-model-movement', 'Жилье', '180', 'EUR', false, '0'),
('MOVEMENT_003', 'Проверка трех вариантов жилья', 'chat-model-movement', 'Жилье', '160', 'EUR', false, '0'),
('MOVEMENT_004', 'Поиск государственной школы (Willkommensklassen)', 'chat-model-movement', 'Образование и дети', '265', 'EUR', false, '0'),
('MOVEMENT_005', 'Поиск детского сада (подбор 5 вариантов, без гарантии места)', 'chat-model-movement', 'Образование и дети', '265', 'EUR', false, '0'),
('MOVEMENT_006', 'Помощь с регистрацией ребенка в детских садах', 'chat-model-movement', 'Образование и дети', '430', 'EUR', false, '0'),
('MOVEMENT_007', 'Оформление в группу продленного дня на 1-2 класс', 'chat-model-movement', 'Образование и дети', '435', 'EUR', false, '0'),
('MOVEMENT_008', 'Помощь с оформлением направления в детский сад (получение kitagutschein)', 'chat-model-movement', 'Образование и дети', '330', 'EUR', false, '0'),
('MOVEMENT_009', 'Координация процесса регистрации в Jobcenter', 'chat-model-movement', 'Социальные услуги и страхование', '350', 'EUR', false, '0'),
('MOVEMENT_010', 'Помощь с оформлением в Jobcenter', 'chat-model-movement', 'Социальные услуги и страхование', '705', 'EUR', false, '0'),
('MOVEMENT_011', 'Помощь в смене тарифа медицинского страхования', 'chat-model-movement', 'Социальные услуги и страхование', '240', 'EUR', false, '0'),
('MOVEMENT_012', 'Общая информация о поставщиках электроэнергии, цифрового телевидения', 'chat-model-movement', 'Консультации и прочее', '120', 'EUR', false, '0');

-- Услуги для бизнеса
INSERT INTO "Services" (reference_id, title, agent_model, category, cost, currency, required_prepaid, prepaid_amount) VALUES
('BUSINESS_001', 'Подготовка и согласование учредительной документации с нотариатом', 'chat-model-bussiness', 'Документы', '500', 'EUR', false, '0'),
('BUSINESS_002', 'Помощь в регистрации фирмы в налоговой службе', 'chat-model-bussiness', 'Документы', '420', 'EUR', false, '0'),
('BUSINESS_003', 'Помощь в регистрации торговой марки (Patentamt)', 'chat-model-bussiness', 'Документы', '600', 'EUR', false, '0'),
('BUSINESS_004', 'Регистрация ИП в Германии', 'chat-model-bussiness', 'Документы', '330', 'EUR', false, '0'),
('BUSINESS_005', 'Регистрация предпринимательской деятельности в Gewerbeamt онлайн', 'chat-model-bussiness', 'Документы', '360', 'EUR', false, '0'),
('BUSINESS_006', 'Внесение изменений в трудовой контракт', 'chat-model-bussiness', 'Документы', '150', 'EUR', false, '0'),
('BUSINESS_007', 'Проверка регистрации фирмы в реестре', 'chat-model-bussiness', 'Документы', '120', 'EUR', false, '0'),
('BUSINESS_008', 'Помощь в подготовке ответа по торговой марке', 'chat-model-bussiness', 'Документы', '360', 'EUR', false, '0'),
('BUSINESS_009', 'Запрос в IHK на проверку допустимости названия фирмы', 'chat-model-bussiness', 'Документы', '120', 'EUR', false, '0'),
('BUSINESS_010', 'Координация процесса регистрации предпринимательской деятельности (консультация)', 'chat-model-bussiness', 'Координация', '99', 'EUR', false, '0'),
('BUSINESS_011', 'Сопровождение процесса внесения изменений / дополнений в устав фирмы', 'chat-model-bussiness', 'Координация', '480', 'EUR', false, '0'),
('BUSINESS_012', 'Помощь в открытии фирменного счета в банке', 'chat-model-bussiness', 'Финансы', '400', 'EUR', false, '0'),
('BUSINESS_013', 'Практическое обучение основам бухучета в Германии', 'chat-model-bussiness', 'Финансы', '550', 'EUR', false, '0'),
('BUSINESS_014', 'Получение налогового номера (Steuernummer) онлайн', 'chat-model-bussiness', 'Финансы', '420', 'EUR', false, '0'),
('BUSINESS_015', 'Запись на термин к нотариусу', 'chat-model-bussiness', 'Сопровождение', '175', 'EUR', false, '0'),
('BUSINESS_016', 'Сопровождение на термин к нотариусу, включая перевод', 'chat-model-bussiness', 'Сопровождение', '350', 'EUR', false, '0');

-- Услуги для фриланса
INSERT INTO "Services" (reference_id, title, agent_model, category, cost, currency, required_prepaid, prepaid_amount) VALUES
('FREELANCE_001', 'Проверка и рекомендации по оптимизации презентации (портфолио)', 'chat-model-freelance', 'Документы', '180', 'EUR', false, '0'),
('FREELANCE_002', 'Оптимизация резюме', 'chat-model-freelance', 'Документы', '175', 'EUR', false, '0'),
('FREELANCE_003', 'Помощь в оптимизации бизнес-плана (предоставление примеров, заполнение по образцу)', 'chat-model-freelance', 'Документы', '240', 'EUR', false, '0');

-- Услуги для виз
INSERT INTO "Services" (reference_id, title, agent_model, category, cost, currency, required_prepaid, prepaid_amount) VALUES
('VISA_001', 'Проверка документов для подачи на национальную визу Д', 'chat-model-visa', 'Документы', '265', 'EUR', false, '0'),
('VISA_002', 'Заполнение анкет для подачи на визу', 'chat-model-visa', 'Документы', '175', 'EUR', false, '0'),
('VISA_003', 'Заполнение заявления на выдачу Национальной визы', 'chat-model-visa', 'Документы', '175', 'EUR', false, '0'),
('VISA_004', 'Помощь в подготовке и подаче документов на получение национальной визы Д', 'chat-model-visa', 'Документы', '615', 'EUR', false, '0'),
('VISA_005', 'Подготовка к собеседованию в посольстве', 'chat-model-visa', 'Подготовка к подаче', '99', 'EUR', false, '0'),
('VISA_006', 'Помощь в открытии блокированного счета', 'chat-model-visa', 'Финансы и страховка', '180', 'EUR', false, '0'),
('VISA_007', 'Помощь в оформлении временного страхового полиса (страховка Incoming)', 'chat-model-visa', 'Финансы и страховка', '79', 'EUR', false, '0');

-- Услуги для ВНЖ
INSERT INTO "Services" (reference_id, title, agent_model, category, cost, currency, required_prepaid, prepaid_amount) VALUES
('VNJ_001', 'Помощь в заполнении анкеты на ВНЖ/ПМЖ/Голубую карту', 'chat-model-vnj', 'Документы', '175', 'EUR', false, '0'),
('VNJ_002', 'Сбор и подача документов для получения ВНЖ для члена семьи', 'chat-model-vnj', 'Документы', '250', 'EUR', false, '0'),
('VNJ_003', 'Оформление документов для получения ВНЖ на одного члена семьи (при условии одновременной подачи)', 'chat-model-vnj', 'Документы', '425', 'EUR', false, '0'),
('VNJ_004', 'Сопровождение на термин в ABH для подачи документов на ВНЖ', 'chat-model-vnj', 'Сопровождение', '179', 'EUR', false, '0');

-- Услуги для ПМЖ
INSERT INTO "Services" (reference_id, title, agent_model, category, cost, currency, required_prepaid, prepaid_amount) VALUES
('PMJ_001', 'Запрос выписки из пенсионного фонда', 'chat-model-pmj', 'Документы', '265', 'EUR', false, '0'),
('PMJ_002', 'Смена адреса прописки в системе пенсионного фонда (если о смене прописки до сих пор не сообщили в ПФ)', 'chat-model-pmj', 'Документы', '99', 'EUR', false, '0'),
('PMJ_003', 'Сбор и проверка документов по чек-листу для получения ПМЖ', 'chat-model-pmj', 'Документы', '265', 'EUR', false, '0'),
('PMJ_004', 'Запись на экзамен Leben in Deutschland', 'chat-model-pmj', 'Документы', '175', 'EUR', false, '0'),
('PMJ_005', 'Помощь в заполнении анкет для получения ПМЖ', 'chat-model-pmj', 'Документы', '175', 'EUR', false, '0');

-- Услуги для гражданства
INSERT INTO "Services" (reference_id, title, agent_model, category, cost, currency, required_prepaid, prepaid_amount) VALUES
('CITIZENSHIP_001', 'Запрос выписки из пенсионного фонда', 'chat-model-residence', 'Документы', '265', 'EUR', false, '0'),
('CITIZENSHIP_002', 'Запрос выписки из страховой компании клиента', 'chat-model-residence', 'Документы', '265', 'EUR', false, '0'),
('CITIZENSHIP_003', 'Смена адреса прописки в системе пенсионного фонда (если о смене прописки до сих пор не сообщили в ПФ)', 'chat-model-residence', 'Документы', '99', 'EUR', false, '0'),
('CITIZENSHIP_004', 'Запись на экзамен Leben in Deutschland', 'chat-model-residence', 'Документы', '175', 'EUR', false, '0'),
('CITIZENSHIP_005', 'Сбор и проверка документов по чек-листу для получения гражданства', 'chat-model-residence', 'Документы', '265', 'EUR', false, '0'),
('CITIZENSHIP_006', 'Помощь в заполнении анкет для получения гражданства', 'chat-model-residence', 'Документы', '360', 'EUR', false, '0'),
('CITIZENSHIP_007', 'Запись на термин в Ведомство по делам иностранцев', 'chat-model-residence', 'Документы', '175', 'EUR', false, '0'),
('CITIZENSHIP_008', 'Запись на прием в Bürgeramt для получения паспорта', 'chat-model-residence', 'Документы', '175', 'EUR', false, '0'),
('CITIZENSHIP_009', 'Сопровождение в Bürgeramt для получения паспорта', 'chat-model-residence', 'Сопровождение', '179', 'EUR', false, '0');
