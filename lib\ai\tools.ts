import { initializeAllTools } from '@/lib/mcp/adapter';
import { loadMcpConfig, getMCPConfigList } from '@/lib/mcp/load.config';
import { MCPService } from '@/lib/mcp/service';
import { callMcpGetLeads } from '@/lib/ai/tools/bitrix/get-leads';
import { callMcpCreateLead } from '@/lib/ai/tools/bitrix/create-lead';
import { performRagQuery } from '@/lib/ai/tools/crawl4ai-rag/perform-rag-query';
import { getChatActivityMetrics } from '@/lib/ai/tools/analytics/get-activities-by-filter';
import { getPotentialLeads } from '@/lib/ai/tools/orders/get-potential-leads';
import { getPotentialLeadDetails } from '@/lib/ai/tools/orders/get-potential-lead-details';
import type { Session } from 'next-auth';
import { UserRole } from '@/app/(auth)/auth.config';

/**
 * Инструменты для обычных пользователей
 */
const USER_TOOLS = ['performRagQuery'] as const;

/**
 * Инструменты для модераторов
 */
const MODERATOR_TOOLS = ['getChatActivityMetrics', 'createLead', 'getPotentialLeads', 'getPotentialLeadDetails'] as const;

/**
 * Инструменты для администраторов
 */
const ADMIN_TOOLS = [...MODERATOR_TOOLS, ...USER_TOOLS, 'getLeads'] as const;

export type ToolName =
  | (typeof USER_TOOLS)[number]
  | (typeof ADMIN_TOOLS)[number]
  | (typeof MODERATOR_TOOLS)[number];

// Глобальная ссылка на MCP сервис для отключения
let globalMcpService: MCPService | null = null;

/**
 * Инициализирует все доступные инструменты из MCP конфигурации
 */
export async function initializeTools() {
  try {
    const config_mcp = await loadMcpConfig();
    const mcpService = new MCPService(getMCPConfigList(<any>config_mcp));
    const { allTools, toolList } = await initializeAllTools(mcpService);

    // Сохраняем ссылку на сервис для последующего отключения
    globalMcpService = mcpService;

    console.log('Initialized MCP tools:', allTools, toolList);
    
    return { allTools, toolList };
  } catch (error) {
    console.error('Failed to initialize MCP tools:', error);
    // Возвращаем пустые инструменты в случае ошибки
    return { allTools: {}, toolList: [] };
  }
}

/**
 * Возвращает список инструментов в зависимости от роли пользователя
 */
export function getToolsForRole(role: UserRole | undefined): string[] {
  switch (role) {
    case UserRole.ADMIN:
      return [...ADMIN_TOOLS];
    case UserRole.MODERATOR:
      return [...MODERATOR_TOOLS];
    case UserRole.USER:
    default:
      return [...USER_TOOLS];
  }
}

/**
 * Объединяет MCP инструменты с инструментами для конкретной роли
 */
export async function getActiveToolsForRole(
  role: UserRole | undefined,
): Promise<string[]> {
  try {
    const { toolList } = await initializeTools();
    const roleTools = getToolsForRole(role);

    // Объединяем MCP инструменты с инструментами для роли
    const allActiveTools = [...(toolList as string[]), ...roleTools];

    // Убираем дубликаты
    return [...new Set(allActiveTools)];
  } catch (error) {
    console.error('Failed to get active tools for role:', error);
    // В случае ошибки возвращаем только роль-специфичные инструменты
    return getToolsForRole(role);
  }
}

/**
 * Проверяет, имеет ли пользователь доступ к определенному инструменту
 */
export function hasToolAccess(
  role: UserRole | undefined,
  toolName: string,
): boolean {
  const availableTools = getToolsForRole(role);
  return availableTools.includes(toolName);
}

/**
 * Возвращает объект инструментов для конкретной роли пользователя
 */
export async function getToolsObjectForRole(
  role: UserRole | undefined,
  session: Session,
  dataStream: any,
): Promise<Record<string, any>> {
  let allTools = {};
  
  try {
    const result = await initializeTools();
    allTools = result.allTools;
  } catch (error) {
    console.error('Failed to initialize MCP tools in getToolsObjectForRole:', error);
    // Продолжаем с пустыми MCP инструментами
  }
  
  const availableTools = getToolsForRole(role);

  const toolsObject: Record<string, any> = {
    ...allTools,
  };

  // Добавляем инструменты только если они доступны для роли
  if (availableTools.includes('performRagQuery')) {
    toolsObject.performRagQuery = performRagQuery({
      session,
      dataStream,
    });
  }

  if (availableTools.includes('getChatActivityMetrics')) {
    toolsObject.getChatActivityMetrics = getChatActivityMetrics({
      session,
      dataStream,
    });
  }

  if (availableTools.includes('getLeads')) {
    toolsObject.getLeads = callMcpGetLeads({
      session,
      dataStream,
    });
  }

  if (availableTools.includes('createLead')) {
    toolsObject.createLead = callMcpCreateLead({
      session,
      dataStream,
    });
  }

  if (availableTools.includes('getPotentialLeads')) {
    toolsObject.getPotentialLeads = getPotentialLeads({ session, dataStream });
  }

  if (availableTools.includes('getPotentialLeadDetails')) {
    toolsObject.getPotentialLeadDetails = getPotentialLeadDetails({ session, dataStream });
  }

  return toolsObject;
}

/**
 * Отключает все MCP сервисы
 */
export function disconnectAllMcpServices() {
  if (globalMcpService) {
    globalMcpService.disconnectAll();
    globalMcpService = null;
  }
}
