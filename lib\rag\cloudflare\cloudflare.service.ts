// lib/rag/cloudflare/cloudflareService.ts
import { RagService } from "@/lib/rag/rag.service.interface";
import { CloudflareAiClient } from "@/lib/rag/cloudflare/cfautorag.client";
import {AIModel, AiSearchRequest, AiSearchResponse} from "@/lib/rag/cloudflare/cfaisearch.types";


export class CloudflareRagService implements RagService {
    private client: CloudflareAiClient;

    constructor(accountId: string, autoragName: string, apiToken: string) {
        if(!accountId || !autoragName || !apiToken) throw new Error("CloudflareRagService constructor called with invalid arguments");
        this.client = new CloudflareAiClient(accountId, autoragName, apiToken);
    }

    async findSimilarDocs(query: string): Promise<AiSearchResponse> {
        const request: AiSearchRequest = {
            query: query,
            model: <string>AIModel.CONTEXT24K,
            rewrite_query: true,
            max_num_results: 10,
        };
        return this.client.search(request);
    }

    async uploadDocuments(sourceDir: string): Promise<void> {
        throw new Error("CloudflareRagService upload documents not released");
    }

    async processDocuments(): Promise<void> {
        throw new Error("CloudflareRagService process documents not released");
    }
}
