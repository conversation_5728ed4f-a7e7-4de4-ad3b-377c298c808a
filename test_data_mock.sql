INSERT INTO "User" (id, name, email, role, password)
VALUES (
        '11111111-1111-1111-1111-111111111111',
        '<PERSON><PERSON><PERSON>овна Петрова',
        '<EMAIL>',
        'user',
        '$2a$10$hashedpassword1'
    ),
    (
        '22222222-2222-2222-2222-************',
        '<PERSON>и<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>рович Сидоров',
        'mikha<PERSON>.sidor<PERSON>@yandex.ru',
        'user',
        '$2a$10$hashedpassword2'
    ),
    (
        '33333333-3333-3333-3333-************',
        'Елена Викторовна Козлова',
        '<EMAIL>',
        'user',
        '$2a$10$hashedpassword3'
    ),
    (
        '44444444-4444-4444-4444-************',
        'Дмитр<PERSON> <PERSON>и<PERSON><PERSON><PERSON><PERSON>лк<PERSON>',
        'd<PERSON><PERSON>.<EMAIL>',
        'user',
        '$2a$10$hashedpassword4'
    ),
    (
        '55555555-5555-5555-5555-************',
        'Ольга Сергеевна Морозова',
        '<EMAIL>',
        'user',
        '$2a$10$hashedpassword5'
    ),
    (
        '66666666-6666-6666-6666-************',
        'Алексей Петрович Новиков',
        '<EMAIL>',
        'user',
        '$2a$10$hashedpassword6'
    ),
    (
        '77777777-7777-7777-7777-************',
        'Мария Ивановна Соколова',
        '<EMAIL>',
        'user',
        '$2a$10$hashedpassword7'
    ),
    (
        '88888888-8888-8888-8888-************',
        'Сергей Николаевич Лебедев',
        '<EMAIL>',
        'user',
        '$2a$10$hashedpassword8'
    ),
    (
        '*************-9999-9999-************',
        'Татьяна Михайловна Орлова',
        '<EMAIL>',
        'user',
        '$2a$10$hashedpassword9'
    ),
    (
        'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa',
        'Владимир Петрович Медведев',
        '<EMAIL>',
        'user',
        '$2a$10$hashedpassword10'
    ),
    (
        '5217d2fe-66e9-400a-abe8-f94767e9cfcc',
        'BCA Moderator',
        '<EMAIL>',
        'moderator',
        '$2a$10$87IN4ZJmH7XXnvo9y/JnP.s7p8K5M/ztPE8Bj7MnWlz1A1lyRYIQi'
    );
ALTER SEQUENCE "Order_id_seq" RESTART WITH 1;
INSERT INTO "Order" (
        status,
        recipient,
        date_created,
        date_updated
    )
VALUES (
        'new',
        '{"name": "Анна", "secondName": "Ивановна", "lastName": "Петрова", "email": "<EMAIL>", "phone": "+49 176 12345678", "comments": "Нужна помощь с признанием медицинского диплома"}',
        '2024-01-15 10:30:00+00',
        '2024-01-15 10:30:00+00'
    ),
    (
        'new',
        '{"name": "Михаил", "secondName": "Александрович", "lastName": "Сидоров", "email": "<EMAIL>", "phone": "+49 152 87654321", "comments": "Планирую переезд в Берлин с семьей"}',
        '2024-01-18 14:20:00+00',
        '2024-01-18 14:20:00+00'
    ),
    (
        'new',
        '{"name": "Елена", "secondName": "Викторовна", "lastName": "Козлова", "email": "<EMAIL>", "phone": "+49 170 11223344", "comments": "Хочу открыть ИП в Германии"}',
        '2024-01-20 09:15:00+00',
        '2024-01-22 16:45:00+00'
    ),
    (
        'new',
        '{"name": "Дмитрий", "secondName": "Викторович", "lastName": "Волков", "email": "<EMAIL>", "phone": "+49 175 55667788", "comments": "Нужна национальная виза"}',
        '2024-01-25 11:45:00+00',
        '2024-01-25 11:45:00+00'
    ),
    (
        'new',
        '{"name": "Ольга", "secondName": "Сергеевна", "lastName": "Морозова", "email": "<EMAIL>", "phone": "+49 160 99887766", "comments": "Подача на ВНЖ для всей семьи"}',
        '2024-02-01 13:30:00+00',
        '2024-02-01 13:30:00+00'
    ),
    (
        'new',
        '{"name": "Алексей", "secondName": "Петрович", "lastName": "Новиков", "email": "<EMAIL>", "phone": "+49 178 44556677", "comments": "Фриланс в IT сфере"}',
        '2024-02-05 08:20:00+00',
        '2024-02-07 12:10:00+00'
    ),
    (
        'new',
        '{"name": "Мария", "secondName": "Ивановна", "lastName": "Соколова", "email": "<EMAIL>", "phone": "+49 151 22334455", "comments": "Получение ПМЖ"}',
        '2024-02-10 15:40:00+00',
        '2024-02-10 15:40:00+00'
    ),
    (
        'new',
        '{"name": "Сергей", "secondName": "Николаевич", "lastName": "Лебедев", "email": "<EMAIL>", "phone": "+49 162 66778899", "comments": "Регистрация бизнеса"}',
        '2024-02-15 10:10:00+00',
        '2024-02-15 10:10:00+00'
    ),
    (
        'new',
        '{"name": "Татьяна", "secondName": "Михайловна", "lastName": "Орлова", "email": "<EMAIL>", "phone": "+49 173 33445566", "comments": "Подача на гражданство"}',
        '2024-02-20 12:25:00+00',
        '2024-02-22 09:30:00+00'
    ),
    (
        'new',
        '{"name": "Владимир", "secondName": "Петрович", "lastName": "Медведев", "email": "<EMAIL>", "phone": "+49 157 77889900", "comments": "Переезд и поиск жилья"}',
        '2024-02-25 16:50:00+00',
        '2024-02-25 16:50:00+00'
    );
INSERT INTO "OrderItem" (
        order_id,
        service_id,
        title,
        agent_model,
        quantity,
        price,
        currency
    )
VALUES (
        1,
        'DIPLOM_004',
        'Сбор документов для подтверждения медицинского диплома',
        'chat-model-diplom',
        1,
        '1350',
        'EUR'
    ),
    (
        1,
        'DIPLOM_005',
        'Координация процесса признания диплома сотрудником ВСА',
        'chat-model-diplom',
        1,
        '560',
        'EUR'
    ),
    (
        2,
        'MOVEMENT_001',
        'Заключение договора аренды жилья',
        'chat-model-movement',
        1,
        '770',
        'EUR'
    ),
    (
        2,
        'MOVEMENT_004',
        'Поиск государственной школы (Willkommensklassen)',
        'chat-model-movement',
        1,
        '265',
        'EUR'
    ),
    (
        2,
        'MOVEMENT_005',
        'Поиск детского сада (подбор 5 вариантов, без гарантии места)',
        'chat-model-movement',
        1,
        '265',
        'EUR'
    ),
    (
        3,
        'BUSINESS_004',
        'Регистрация ИП в Германии',
        'chat-model-bussiness',
        1,
        '330',
        'EUR'
    ),
    (
        3,
        'BUSINESS_014',
        'Получение налогового номера (Steuernummer) онлайн',
        'chat-model-bussiness',
        1,
        '420',
        'EUR'
    ),
    (
        4,
        'VISA_004',
        'Помощь в подготовке и подаче документов на получение национальной визы Д',
        'chat-model-visa',
        1,
        '615',
        'EUR'
    ),
    (
        4,
        'VISA_006',
        'Помощь в открытии блокированного счета',
        'chat-model-visa',
        1,
        '180',
        'EUR'
    ),
    (
        5,
        'VNJ_002',
        'Сбор и подача документов для получения ВНЖ для члена семьи',
        'chat-model-vnj',
        2,
        '250',
        'EUR'
    ),
    (
        5,
        'VNJ_004',
        'Сопровождение на термин в ABH для подачи документов на ВНЖ',
        'chat-model-vnj',
        1,
        '179',
        'EUR'
    ),
    (
        6,
        'FREELANCE_001',
        'Проверка и рекомендации по оптимизации презентации (портфолио)',
        'chat-model-freelance',
        1,
        '180',
        'EUR'
    ),
    (
        6,
        'FREELANCE_002',
        'Оптимизация резюме',
        'chat-model-freelance',
        1,
        '175',
        'EUR'
    ),
    (
        7,
        'PMJ_003',
        'Сбор и проверка документов по чек-листу для получения ПМЖ',
        'chat-model-pmj',
        1,
        '265',
        'EUR'
    ),
    (
        7,
        'PMJ_004',
        'Запись на экзамен Leben in Deutschland',
        'chat-model-pmj',
        1,
        '175',
        'EUR'
    ),
    (
        8,
        'BUSINESS_001',
        'Подготовка и согласование учредительной документации с нотариатом',
        'chat-model-bussiness',
        1,
        '500',
        'EUR'
    ),
    (
        8,
        'BUSINESS_012',
        'Помощь в открытии фирменного счета в банке',
        'chat-model-bussiness',
        1,
        '400',
        'EUR'
    ),
    (
        9,
        'CITIZENSHIP_005',
        'Сбор и проверка документов по чек-листу для получения гражданства',
        'chat-model-residence',
        1,
        '265',
        'EUR'
    ),
    (
        9,
        'CITIZENSHIP_006',
        'Помощь в заполнении анкет для получения гражданства',
        'chat-model-residence',
        1,
        '360',
        'EUR'
    ),
    (
        10,
        'MOVEMENT_002',
        'Подбор трех вариантов жилья',
        'chat-model-movement',
        1,
        '180',
        'EUR'
    ),
    (
        10,
        'MOVEMENT_009',
        'Координация процесса регистрации в Jobcenter',
        'chat-model-movement',
        1,
        '350',
        'EUR'
    );
INSERT INTO "Chat" (
        id,
        "createdAt",
        title,
        "agentModel",
        "userId",
        visibility
    )
VALUES (
        '11111111-aaaa-bbbb-cccc-111111111111',
        '2024-01-15 10:35:00+00',
        'Признание медицинского диплома',
        'chat-model-diplom',
        '11111111-1111-1111-1111-111111111111',
        'private'
    ),
    (
        '22222222-aaaa-bbbb-cccc-************',
        '2024-01-18 14:25:00+00',
        'Переезд в Берлин с семьей',
        'chat-model-movement',
        '22222222-2222-2222-2222-************',
        'private'
    ),
    (
        '33333333-aaaa-bbbb-cccc-************',
        '2024-01-20 09:20:00+00',
        'Открытие ИП в Германии',
        'chat-model-bussiness',
        '33333333-3333-3333-3333-************',
        'private'
    ),
    (
        '44444444-aaaa-bbbb-cccc-************',
        '2024-01-25 11:50:00+00',
        'Получение национальной визы',
        'chat-model-visa',
        '44444444-4444-4444-4444-************',
        'private'
    ),
    (
        '55555555-aaaa-bbbb-cccc-************',
        '2024-02-01 13:35:00+00',
        'ВНЖ для семьи',
        'chat-model-vnj',
        '55555555-5555-5555-5555-************',
        'private'
    ),
    (
        '66666666-aaaa-bbbb-cccc-************',
        '2024-02-05 08:25:00+00',
        'IT фриланс в Германии',
        'chat-model-freelance',
        '66666666-6666-6666-6666-************',
        'private'
    ),
    (
        '77777777-aaaa-bbbb-cccc-************',
        '2024-02-10 15:45:00+00',
        'Получение ПМЖ',
        'chat-model-pmj',
        '77777777-7777-7777-7777-************',
        'private'
    ),
    (
        '88888888-aaaa-bbbb-cccc-************',
        '2024-02-15 10:15:00+00',
        'Регистрация бизнеса',
        'chat-model-bussiness',
        '88888888-8888-8888-8888-************',
        'private'
    ),
    (
        '99999999-aaaa-bbbb-cccc-************',
        '2024-02-20 12:30:00+00',
        'Подача на гражданство',
        'chat-model-residence',
        '*************-9999-9999-************',
        'private'
    ),
    (
        'aaaaaaaa-aaaa-bbbb-cccc-aaaaaaaaaaaa',
        '2024-02-25 16:55:00+00',
        'Переезд и поиск жилья',
        'chat-model-movement',
        'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa',
        'private'
    );
INSERT INTO "Message_v2" (
        id,
        "chatId",
        role,
        parts,
        attachments,
        "createdAt"
    )
VALUES (
        gen_random_uuid(),
        '11111111-aaaa-bbbb-cccc-111111111111',
        'user',
        '[{"type": "text", "text": ""}]',
        '[]',
        '2024-01-15 10:35:00+00'
    ),
    (
        gen_random_uuid(),
        '11111111-aaaa-bbbb-cccc-111111111111',
        'assistant',
        '[{"type": "text", "text": "Добро пожаловать! Я специалист по признанию дипломов в Германии. Помогу вам пройти весь процесс от сбора документов до получения разрешения на работу. Расскажите о вашей ситуации."}]',
        '[]',
        '2024-01-15 10:35:30+00'
    ),
    (
        gen_random_uuid(),
        '11111111-aaaa-bbbb-cccc-111111111111',
        'user',
        '[{"type": "text", "text": "Здравствуйте! Я врач из России, хочу признать свой диплом в Германии. С чего начать?"}]',
        '[]',
        '2024-01-15 10:36:00+00'
    ),
    (
        gen_random_uuid(),
        '11111111-aaaa-bbbb-cccc-111111111111',
        'assistant',
        '[{"type": "text", "text": "Для признания медицинского диплома в Германии нужно пройти несколько этапов. Сначала необходимо собрать полный пакет документов и подать их в соответствующее ведомство федеральной земли. Я могу помочь вам с этим процессом."}]',
        '[]',
        '2024-01-15 10:37:00+00'
    ),
    (
        gen_random_uuid(),
        '11111111-aaaa-bbbb-cccc-111111111111',
        'user',
        '[{"type": "text", "text": "Какие документы нужны и сколько это будет стоить?"}]',
        '[]',
        '2024-01-15 10:37:00+00'
    ),
    (
        gen_random_uuid(),
        '11111111-aaaa-bbbb-cccc-111111111111',
        'assistant',
        '[{"type": "text", "text": "Основные документы: диплом с приложением, справка о содержании обучения, справка о практике, переводы и апостили. Стоимость моих услуг: сбор документов - 1350€, координация процесса - 560€. Готов предложить пакет услуг."}]',
        '[]',
        '2024-01-15 10:38:00+00'
    ),
    (
        gen_random_uuid(),
        '22222222-aaaa-bbbb-cccc-************',
        'user',
        '[{"type": "text", "text": ""}]',
        '[]',
        '2024-01-18 14:25:00+00'
    ),
    (
        gen_random_uuid(),
        '22222222-aaaa-bbbb-cccc-************',
        'assistant',
        '[{"type": "text", "text": "Здравствуйте! Я эксперт по переезду в Германию. Помогу организовать ваш переезд: найти жилье, устроить детей в школу, решить бытовые вопросы. Что вас интересует?"}]',
        '[]',
        '2024-01-18 14:25:30+00'
    ),
    (
        gen_random_uuid(),
        '22222222-aaaa-bbbb-cccc-************',
        'user',
        '[{"type": "text", "text": "Планируем переезд в Берлин с женой и двумя детьми. Нужна помощь с жильем и школой."}]',
        '[]',
        '2024-01-18 14:26:00+00'
    ),
    (
        gen_random_uuid(),
        '22222222-aaaa-bbbb-cccc-************',
        'assistant',
        '[{"type": "text", "text": "Отлично! Помогу организовать переезд. Для семьи с детьми важно найти подходящее жилье и устроить детей в школу. Могу предложить: поиск жилья, заключение договора аренды, поиск школы и детского сада."}]',
        '[]',
        '2024-01-18 14:27:00+00'
    ),
    (
        gen_random_uuid(),
        '22222222-aaaa-bbbb-cccc-************',
        'user',
        '[{"type": "text", "text": "Да, это именно то что нам нужно. Какие сроки и стоимость?"}]',
        '[]',
        '2024-01-18 14:27:00+00'
    ),
    (
        gen_random_uuid(),
        '22222222-aaaa-bbbb-cccc-************',
        'assistant',
        '[{"type": "text", "text": "Сроки: поиск жилья - 3-5 дней, заключение договора аренды - 1-2 дня, поиск школы и детского сада - 3-5 дней. Стоимость: поиск жилья - 770€, заключение договора аренды - 265€, поиск школы и детского сада - 265€."}]',
        '[]',
        '2024-01-18 14:28:00+00'
    ),
    (
        gen_random_uuid(),
        '33333333-aaaa-bbbb-cccc-************',
        'user',
        '[{"type": "text", "text": ""}]',
        '[]',
        '2024-01-20 09:20:00+00'
    ),
    (
        gen_random_uuid(),
        '33333333-aaaa-bbbb-cccc-************',
        'assistant',
        '[{"type": "text", "text": "Добро пожаловать! Я консультант по ведению бизнеса в Германии. Помогу с регистрацией ИП, открытием ООО, получением лицензий и всеми формальностями. Чем могу помочь?"}]',
        '[]',
        '2024-01-20 09:20:30+00'
    ),
    (
        gen_random_uuid(),
        '33333333-aaaa-bbbb-cccc-************',
        'user',
        '[{"type": "text", "text": "Хочу открыть ИП в Германии для консультационных услуг. Что для этого нужно?"}]',
        '[]',
        '2024-01-20 09:21:00+00'
    ),
    (
        gen_random_uuid(),
        '33333333-aaaa-bbbb-cccc-************',
        'assistant',
        '[{"type": "text", "text": "Для регистрации ИП в Германии нужно: регистрация в Gewerbeamt, получение налогового номера, возможно регистрация в IHK. Стоимость моих услуг: регистрация ИП - 330€, получение Steuernummer - 420€."}]',
        '[]',
        '2024-01-20 09:22:00+00'
    ),
    (
        gen_random_uuid(),
        '44444444-aaaa-bbbb-cccc-************',
        'user',
        '[{"type": "text", "text": ""}]',
        '[]',
        '2024-01-25 11:50:00+00'
    ),
    (
        gen_random_uuid(),
        '44444444-aaaa-bbbb-cccc-************',
        'assistant',
        '[{"type": "text", "text": "Здравствуйте! Я специалист по визовым вопросам. Помогу получить любой тип визы в Германию: туристическую, рабочую, национальную. Расскажите о ваших планах."}]',
        '[]',
        '2024-01-25 11:50:30+00'
    ),
    (
        gen_random_uuid(),
        '44444444-aaaa-bbbb-cccc-************',
        'user',
        '[{"type": "text", "text": "Нужна национальная виза для работы в Германии. Какие документы требуются?"}]',
        '[]',
        '2024-01-25 11:51:00+00'
    ),
    (
        gen_random_uuid(),
        '44444444-aaaa-bbbb-cccc-************',
        'assistant',
        '[{"type": "text", "text": "Для национальной визы D нужен полный пакет документов: анкета, фото, паспорт, приглашение работодателя, диплом, справки, страховка, блокированный счет. Помогу с подготовкой и подачей - 615€, плюс открытие счета - 180€."}]',
        '[]',
        '2024-01-25 11:52:00+00'
    ),
    (
        gen_random_uuid(),
        '55555555-aaaa-bbbb-cccc-************',
        'user',
        '[{"type": "text", "text": ""}]',
        '[]',
        '2024-02-01 13:35:00+00'
    ),
    (
        gen_random_uuid(),
        '55555555-aaaa-bbbb-cccc-************',
        'assistant',
        '[{"type": "text", "text": "Добро пожаловать! Я эксперт по получению ВНЖ в Германии. Помогу собрать документы, записаться на прием в ABH и пройти всю процедуру. О чем хотите узнать?"}]',
        '[]',
        '2024-02-01 13:35:30+00'
    ),
    (
        gen_random_uuid(),
        '55555555-aaaa-bbbb-cccc-************',
        'user',
        '[{"type": "text", "text": "Нужно подать на ВНЖ для всей семьи (я, муж, двое детей). Помогите с документами."}]',
        '[]',
        '2024-02-01 13:36:00+00'
    ),
    (
        gen_random_uuid(),
        '55555555-aaaa-bbbb-cccc-************',
        'assistant',
        '[{"type": "text", "text": "Конечно! Для семьи из 4 человек подготовлю документы на ВНЖ. Стоимость: сбор документов для члена семьи - 250€ за каждого (500€ за двоих), сопровождение в ABH - 179€."}]',
        '[]',
        '2024-02-01 13:37:00+00'
    ),
    (
        gen_random_uuid(),
        '66666666-aaaa-bbbb-cccc-************',
        'user',
        '[{"type": "text", "text": ""}]',
        '[]',
        '2024-02-05 08:25:00+00'
    ),
    (
        gen_random_uuid(),
        '66666666-aaaa-bbbb-cccc-************',
        'assistant',
        '[{"type": "text", "text": "Привет! Я консультант по фрилансу в Германии. Помогу оптимизировать портфолио, составить резюме, найти клиентов и решить налоговые вопросы. Чем занимаетесь?"}]',
        '[]',
        '2024-02-05 08:25:30+00'
    ),
    (
        gen_random_uuid(),
        '66666666-aaaa-bbbb-cccc-************',
        'user',
        '[{"type": "text", "text": "Работаю IT-фрилансером, хочу оптимизировать портфолио и резюме для немецкого рынка."}]',
        '[]',
        '2024-02-05 08:26:00+00'
    ),
    (
        gen_random_uuid(),
        '66666666-aaaa-bbbb-cccc-************',
        'assistant',
        '[{"type": "text", "text": "Отлично! Помогу адаптировать ваше портфолио и резюме под немецкие стандарты. Услуги: оптимизация портфолио - 180€, оптимизация резюме - 175€. Это поможет выделиться среди конкурентов."}]',
        '[]',
        '2024-02-05 08:27:00+00'
    ),
    (
        gen_random_uuid(),
        '77777777-aaaa-bbbb-cccc-************',
        'user',
        '[{"type": "text", "text": ""}]',
        '[]',
        '2024-02-10 15:45:00+00'
    ),
    (
        gen_random_uuid(),
        '77777777-aaaa-bbbb-cccc-************',
        'assistant',
        '[{"type": "text", "text": "Здравствуйте! Я специалист по получению ПМЖ в Германии. Помогу собрать документы, подготовиться к экзаменам и пройти всю процедуру. Расскажите о вашей ситуации."}]',
        '[]',
        '2024-02-10 15:45:30+00'
    ),
    (
        gen_random_uuid(),
        '77777777-aaaa-bbbb-cccc-************',
        'user',
        '[{"type": "text", "text": "Живу в Германии 5 лет, хочу получить ПМЖ. Какие документы нужны?"}]',
        '[]',
        '2024-02-10 15:46:00+00'
    ),
    (
        gen_random_uuid(),
        '77777777-aaaa-bbbb-cccc-************',
        'assistant',
        '[{"type": "text", "text": "Для ПМЖ нужны: справки о доходах, выписка из пенсионного фонда, справка о знании языка, сертификат Leben in Deutschland. Помогу: сбор документов - 265€, запись на экзамен - 175€."}]',
        '[]',
        '2024-02-10 15:47:00+00'
    ),
    (
        gen_random_uuid(),
        '88888888-aaaa-bbbb-cccc-************',
        'user',
        '[{"type": "text", "text": ""}]',
        '[]',
        '2024-02-15 10:15:00+00'
    ),
    (
        gen_random_uuid(),
        '88888888-aaaa-bbbb-cccc-************',
        'assistant',
        '[{"type": "text", "text": "Добро пожаловать! Я консультант по корпоративному праву в Германии. Помогу зарегистрировать ООО, оформить все документы и открыть банковские счета. Что планируете?"}]',
        '[]',
        '2024-02-15 10:15:30+00'
    ),
    (
        gen_random_uuid(),
        '88888888-aaaa-bbbb-cccc-************',
        'user',
        '[{"type": "text", "text": "Хочу зарегистрировать ООО в Германии для торговли. Нужна помощь с документами."}]',
        '[]',
        '2024-02-15 10:16:00+00'
    ),
    (
        gen_random_uuid(),
        '88888888-aaaa-bbbb-cccc-************',
        'assistant',
        '[{"type": "text", "text": "Для регистрации ООО нужно: подготовка устава, нотариальное заверение, регистрация в суде, открытие счета. Стоимость: подготовка документов - 500€, помощь с банковским счетом - 400€."}]',
        '[]',
        '2024-02-15 10:17:00+00'
    ),
    (
        gen_random_uuid(),
        '99999999-aaaa-bbbb-cccc-************',
        'user',
        '[{"type": "text", "text": ""}]',
        '[]',
        '2024-02-20 12:30:00+00'
    ),
    (
        gen_random_uuid(),
        '99999999-aaaa-bbbb-cccc-************',
        'assistant',
        '[{"type": "text", "text": "Здравствуйте! Я специалист по натурализации в Германии. Помогу подготовить документы для получения немецкого гражданства и пройти все этапы. Сколько лет живете в Германии?"}]',
        '[]',
        '2024-02-20 12:30:30+00'
    ),
    (
        gen_random_uuid(),
        '99999999-aaaa-bbbb-cccc-************',
        'user',
        '[{"type": "text", "text": "Живу в Германии 8 лет, хочу подать на гражданство. Что нужно подготовить?"}]',
        '[]',
        '2024-02-20 12:31:00+00'
    ),
    (
        gen_random_uuid(),
        '99999999-aaaa-bbbb-cccc-************',
        'assistant',
        '[{"type": "text", "text": "Для гражданства нужен полный пакет документов: справки о доходах, выписки из фондов, языковые сертификаты, справки о несудимости. Услуги: сбор документов - 265€, заполнение анкет - 360€."}]',
        '[]',
        '2024-02-20 12:32:00+00'
    ),
    (
        gen_random_uuid(),
        'aaaaaaaa-aaaa-bbbb-cccc-aaaaaaaaaaaa',
        'user',
        '[{"type": "text", "text": ""}]',
        '[]',
        '2024-02-25 16:55:00+00'
    ),
    (
        gen_random_uuid(),
        'aaaaaaaa-aaaa-bbbb-cccc-aaaaaaaaaaaa',
        'assistant',
        '[{"type": "text", "text": "Привет! Я эксперт по переезду в Германию. Помогу найти жилье, зарегистрироваться в ведомствах, решить социальные вопросы. Куда планируете переезжать?"}]',
        '[]',
        '2024-02-25 16:55:30+00'
    ),
    (
        gen_random_uuid(),
        'aaaaaaaa-aaaa-bbbb-cccc-aaaaaaaaaaaa',
        'user',
        '[{"type": "text", "text": "Переезжаю в Мюнхен, нужна помощь с поиском жилья и регистрацией в Jobcenter."}]',
        '[]',
        '2024-02-25 16:56:00+00'
    ),
    (
        gen_random_uuid(),
        'aaaaaaaa-aaaa-bbbb-cccc-aaaaaaaaaaaa',
        'assistant',
        '[{"type": "text", "text": "Помогу с переездом в Мюнхен! Предлагаю: подбор жилья (3 варианта) - 180€, координация регистрации в Jobcenter - 350€. Это поможет быстро обустроиться на новом месте."}]',
        '[]',
        '2024-02-25 16:57:00+00'
    );