// lib/rag/vectorDB/pgVector.service.ts
import { RagService } from "@/lib/rag/rag.service.interface";
import { AiSearchResponse } from "@/lib/rag/cloudflare/cfaisearch.types";
import { uploadDocuments } from "@/lib/rag/vectorDB/operations.pgvec";
import { pgvectorProcessdoc } from "@/lib/rag/vectorDB/operations.pgvec";
import { findSimilarDocs } from "@/lib/rag/vectorDB/operations.pgvec";

export class PostgresRagService implements RagService {
    async findSimilarDocs(query: string): Promise<AiSearchResponse> {
        const similarDocs = await findSimilarDocs(query);
        return {
            success: true,
            result: {
                object: "search_result",
                search_query: query,
                response: "Similar documents found",
                data: similarDocs.map(doc => ({
                    file_id: doc.id.toString(),
                    filename: doc.file_name,
                    score: doc.similarity,
                    attributes: {},
                    content: [{ id: doc.id.toString(), type: "text", text: doc.content }]
                })),
                has_more: false,
                next_page: null
            }
        };
    }

    async uploadDocuments(sourceDir: string): Promise<void> {
        uploadDocuments(sourceDir);
    }

    async processDocuments(): Promise<void> {
        await pgvectorProcessdoc();
    }
}
