'use client';

import { useEffect, useState } from 'react';
import { useCookies } from 'next-client-cookies';
import Image from 'next/image';
import classNames from 'classnames';
import {
  getAllLocales,
  isValidLocaleCode,
  Locale,
  LocaleCode,
} from '@/lib/i18n/languages';

export default function HorizontalLanguageSwitcher() {
  const avaliable_languages: Locale[] = getAllLocales();
  const [locale, setLocale] = useState('en');
  const cookies = useCookies();

  useEffect(() => {
    const currentLocale = cookies.get('NEXT_LOCALE') || LocaleCode.EN;
    setLocale(currentLocale);
  }, []);

  const handleLocaleChange = (selectedLocale: string) => {
    selectedLocale = isValidLocaleCode(selectedLocale)
      ? selectedLocale
      : LocaleCode.EN;
    cookies.set('NEXT_LOCALE', selectedLocale);
    setLocale(selectedLocale);
    window.location.reload(); // обновление страницы для применения локали
  };

  return (
    <div className="flex p-2 gap-2">
      {avaliable_languages.map((loc: Locale) => (
        <button
          key={loc.code}
          onClick={() => handleLocaleChange(loc.code)}
          className={classNames('transition-transform hover:scale-105', {
            'ring-2 ring-white': loc.code === locale,
          })}
        >
          <Image src={loc.flag} alt={loc.alt} width={20} height={20} />
        </button>
      ))}
    </div>
  );
}
