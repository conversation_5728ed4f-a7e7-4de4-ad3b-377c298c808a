import { findSimilarDocs } from '@/lib/rag/vectorDB/operations.pgvec';
import { drizzle } from 'drizzle-orm/postgres-js';
import { eq } from 'drizzle-orm';
import postgres from 'postgres';
import { config } from 'dotenv';
import { ragDocument } from '@/lib/rag/vectorDB/rag.schema';
import { generateEmbedding } from '@/lib/rag/utils/generateEmbeddings';

config({
  path: '.env.local',
});

if (!process.env.POSTGRES_URL) {
  throw new Error('POSTGRES_URL is not defined');
}

const testConnection = postgres(process.env.POSTGRES_URL, { max: 1 });
const testDb = drizzle(testConnection);

async function setup() {
  const doc1 = 'This is the first document with my information about data.';
  const doc2 = 'This is the second with new test data.';
  const embedding1 = await generateEmbedding(doc1);
  const embedding2 = await generateEmbedding(doc2);

  const testDocuments = [
    { id: 1, fileName: 'doc1', content: doc1, embedding: embedding1 },
    { id: 2, fileName: 'doc2', content: doc2, embedding: embedding2 },
  ];

  for (const doc of testDocuments) {
    await testDb.delete(ragDocument).where(eq(ragDocument.fileName, doc.fileName)).execute();
  }

  await testDb.insert(ragDocument).values(testDocuments).execute();

}

async function cleanup() {
  await testDb.delete(ragDocument).execute();
}

async function run() {
  try {
    await setup();

    const testCases = [
      { description: 'new test data', expected: 'This is the second with new test data.' },
    ];

    for (const { description, expected } of testCases) {
      const similarDocs = await findSimilarDocs(description);
      console.log(`\nQuery: "${description}"`);
      console.log(similarDocs);
      if (similarDocs.length === 0) {
        console.log('✅ No similar documents found.');
      } else {
        console.log('🔍 Found:', similarDocs.map(d => d.name));
        //const matched = similarDocs.some(doc => doc.name === expected);Н
        //console.log(matched ? '✅ Match found!' : '❌ Unexpected result!');
      }
    }
  } catch (err) {
    console.error('Error during test execution:', err);
  } finally {
    await cleanup();
    await testConnection.end();
  }
}

run().then(()=>{
  console.log('Test completed.');
  process.exit(0)
});
