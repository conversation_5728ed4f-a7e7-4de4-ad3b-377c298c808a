# Руководство по добавлению инструментов (Tools) в проект

Это руководство описывает процесс создания и интеграции новых инструментов (tools) для LLM-агента, включая отображение прогресса их выполнения.

## Содержание

1. [Структура инструментов](#структура-инструментов)
2. [Создание базового инструмента](#создание-базового-инструмента)
3. [Добавление отображения прогресса](#добавление-отображения-прогресса)
4. [Интеграция с UI компонентами](#интеграция-с-ui-компонентами)
5. [Регистрация инструментов](#регистрация-инструментов)
6. [Примеры инструментов](#примеры-инструментов)
7. [Полный пример интеграции](#полный-пример-интеграции)

## Структура инструментов

Инструменты в проекте следуют общей структуре:

1. Импорт необходимых зависимостей (`tool` из 'ai', `z` из 'zod')
2. Определение параметров с помощью Zod схемы
3. Функция `execute` для выполнения логики инструмента
4. Возврат результата

Базовый шаблон инструмента:

```typescript
import { tool } from 'ai';
import { z } from 'zod';

export const myTool = tool({
  description: 'Description of what the tool does',
  parameters: z.object({
    param1: z.string().describe('Description of parameter 1'),
    param2: z.number().describe('Description of parameter 2'),
  }),
  execute: async ({ param1, param2 }) => {
    // Логика инструмента
    const result = await someFunction(param1, param2);
    return result;
  },
});
```

## Создание базового инструмента

### Шаг 1: Создайте новый файл

Создайте новый файл в директории `lib/ai/tools/` с именем, отражающим функциональность (например, `get-news.ts`).

### Шаг 2: Определите параметры

Используйте Zod для определения параметров инструмента:

```typescript
parameters: z.object({
  topic: z.string().describe('The topic to search news for'),
  limit: z.number().default(5).describe('Number of news items to return'),
}),
```

### Шаг 3: Реализуйте логику

Реализуйте логику в функции `execute`:

```typescript
execute: async ({ topic, limit }) => {
  try {
    const response = await fetch(
      `https://api.example.com/news?q=${encodeURIComponent(topic)}&limit=${limit}`
    );
    
    if (!response.ok) {
      throw new Error(`API returned ${response.status}`);
    }
    
    const data = await response.json();
    return {
      articles: data.articles,
      totalResults: data.totalResults,
    };
  } catch (error) {
    console.error('Error:', error);
    return {
      error: 'Failed to fetch data',
      message: error instanceof Error ? error.message : String(error),
    };
  }
},
```

## Добавление отображения прогресса

Для отображения прогресса выполнения инструмента используйте `DataStreamWriter`:

### Шаг 1: Добавьте параметр dataStream

```typescript
interface MyToolProps {
  dataStream?: DataStreamWriter;
}

export const myTool = ({ dataStream }: MyToolProps = {}) =>
  tool({
    // ...
  });
```

### Шаг 2: Отправляйте обновления прогресса

```typescript
execute: async ({ param1 }) => {
  try {
    if (dataStream) {
      dataStream.writeData({
        type: 'text-delta',
        content: JSON.stringify({
          type: 'tool-progress',
          data: {
            steps: [
              {
                id: '1',
                name: 'Step 1',
                description: 'Description of step 1',
                status: 'in-progress',
              }
            ],
            currentStepId: '1',
            isComplete: false,
            title: 'Tool Execution',
          }
        })
      });
    }
    
    // Выполнение логики...
    
    // Обновление прогресса
    if (dataStream) {
      dataStream.writeData({
        type: 'text-delta',
        content: JSON.stringify({
          type: 'tool-progress',
          data: {
            steps: [
              {
                id: '1',
                name: 'Step 1',
                description: 'Description of step 1',
                status: 'completed',
                progress: 100,
              }
            ],
            isComplete: true,
            title: 'Tool Execution',
          }
        })
      });
    }
    
    return result;
  } catch (error) {
    // Обработка ошибок...
  }
},
```

## Интеграция с UI компонентами

### Шаг 1: Создайте компонент для отображения прогресса

Создайте файл `components/tool-progress.tsx`:

```tsx
'use client';

import { useState, useEffect } from 'react';
import { Progress } from '@/components/ui/progress';
import { Spinner } from '@/components/ui/spinner';
import { CheckIcon, XIcon } from '@/components/icons';

export type ToolProgressStep = {
  id: string;
  name: string;
  description: string;
  status: 'pending' | 'in-progress' | 'completed' | 'error';
  progress?: number; // 0-100
  message?: string;
};

export type ToolProgressProps = {
  steps: ToolProgressStep[];
  currentStepId?: string;
  isComplete: boolean;
  error?: string;
  title: string;
};

export function ToolProgress({
  steps,
  currentStepId,
  isComplete,
  error,
  title,
}: ToolProgressProps) {
  const [overallProgress, setOverallProgress] = useState(0);

  useEffect(() => {
    // Вычисляем общий прогресс на основе шагов
    const completedSteps = steps.filter(
      (step) => step.status === 'completed'
    ).length;
    const inProgressStep = steps.find((step) => step.status === 'in-progress');
    const inProgressValue = inProgressStep?.progress || 0;
    
    const totalProgress = 
      (completedSteps * 100 + inProgressValue) / Math.max(steps.length, 1);
    
    setOverallProgress(totalProgress);
  }, [steps]);

  return (
    <div className="w-full bg-background border rounded-lg p-4 mb-4">
      <div className="flex justify-between items-center mb-2">
        <h3 className="text-lg font-medium">{title}</h3>
        {isComplete ? (
          error ? (
            <div className="flex items-center text-destructive">
              <XIcon className="mr-1" />
              <span>Failed</span>
            </div>
          ) : (
            <div className="flex items-center text-green-500">
              <CheckIcon className="mr-1" />
              <span>Complete</span>
            </div>
          )
        ) : (
          <div className="flex items-center text-muted-foreground">
            <Spinner className="mr-1" />
            <span>In progress</span>
          </div>
        )}
      </div>

      <Progress value={overallProgress} className="mb-4" />

      <div className="space-y-3">
        {steps.map((step) => (
          <div
            key={step.id}
            className="flex items-start gap-3"
          >
            <div className="mt-1">
              {step.status === 'completed' ? (
                <div className="size-5 rounded-full bg-green-500 flex items-center justify-center">
                  <CheckIcon className="text-white size-3" />
                </div>
              ) : step.status === 'error' ? (
                <div className="size-5 rounded-full bg-destructive flex items-center justify-center">
                  <XIcon className="text-white size-3" />
                </div>
              ) : step.status === 'in-progress' ? (
                <Spinner className="size-5" />
              ) : (
                <div className="size-5 rounded-full border border-muted-foreground" />
              )}
            </div>
            
            <div className="flex-1">
              <div className="flex justify-between">
                <p className="font-medium">{step.name}</p>
                {step.status === 'in-progress' && step.progress !== undefined && (
                  <span className="text-sm text-muted-foreground">
                    {Math.round(step.progress)}%
                  </span>
                )}
              </div>
              <p className="text-sm text-muted-foreground">{step.description}</p>
              {step.message && (
                <p className="text-sm mt-1 text-foreground">{step.message}</p>
              )}
              {step.status === 'in-progress' && step.progress !== undefined && (
                <Progress value={step.progress} className="mt-1 h-1" />
              )}
            </div>
          </div>
        ))}
      </div>

      {error && (
        <div className="mt-4 p-3 bg-destructive/10 border border-destructive/20 rounded text-sm text-destructive">
          {error}
        </div>
      )}
    </div>
  );
}
```

### Шаг 2: Создайте артефакт для отображения прогресса

Создайте файл `artifacts/tool-progress/client.tsx`:

```tsx
'use client';

import { Artifact } from '@/components/create-artifact';
import { ToolProgress, ToolProgressStep } from '@/components/tool-progress';

type ToolProgressMetadata = {
  steps: ToolProgressStep[];
  currentStepId?: string;
  isComplete: boolean;
  error?: string;
  title: string;
};

export const toolProgressArtifact = new Artifact<'tool-progress', ToolProgressMetadata>({
  kind: 'tool-progress',
  description: 'Displays progress of tool execution with steps',
  initialize: async ({ setMetadata }) => {
    setMetadata({
      steps: [],
      isComplete: false,
      title: 'Tool Execution',
    });
  },
  onStreamPart: ({ streamPart, setMetadata, setArtifact }) => {
    if (streamPart.type === 'text-delta') {
      try {
        const content = streamPart.content as string;
        
        if (content.includes('"type":"tool-progress"')) {
          const progressData = JSON.parse(content);
          
          if (progressData.type === 'tool-progress') {
            setMetadata((metadata) => ({
              ...metadata,
              ...progressData.data,
            }));
            
            setArtifact((draftArtifact) => ({
              ...draftArtifact,
              isVisible: true,
              status: 'streaming',
            }));
          }
        }
      } catch (e) {
        // Игнорируем ошибки парсинга
      }
    }
    
    if (streamPart.type === 'finish') {
      setMetadata((metadata) => ({
        ...metadata,
        isComplete: true,
      }));
      
      setArtifact((draftArtifact) => ({
        ...draftArtifact,
        status: 'idle',
      }));
    }
  },
  content: ({ metadata }) => {
    return (
      <div className="p-1">
        <ToolProgress
          steps={metadata.steps}
          currentStepId={metadata.currentStepId}
          isComplete={metadata.isComplete}
          error={metadata.error}
          title={metadata.title}
        />
      </div>
    );
  },
  // Дополнительные действия и панель инструментов...
});
```

### Шаг 3: Зарегистрируйте артефакт

Добавьте артефакт в список доступных артефактов в файле `components/artifact.tsx`:

```typescript
import { toolProgressArtifact } from '@/artifacts/tool-progress/client';

export const artifactDefinitions = [
  textArtifact,
  codeArtifact,
  sheetArtifact,
  imageArtifact,
  toolProgressArtifact,
];
```

## Регистрация инструментов

Для использования новых инструментов их нужно зарегистрировать в файле `app/(chat)/api/chat/route.ts`. Это основной файл, где инструменты подключаются к LLM.

### Шаг 1: Импортируйте инструмент

В начале файла `app/(chat)/api/chat/route.ts` добавьте импорт вашего инструмента:

```typescript
import { myNewTool } from '@/lib/ai/tools/my-new-tool';
```

### Шаг 2: Добавьте инструмент в конфигурацию streamText

Найдите функцию `streamText` в файле `app/(chat)/api/chat/route.ts` и добавьте ваш инструмент в объект `tools`:

```typescript
return createDataStreamResponse({
  execute: (dataStream) => {
    const result = streamText({
      model: myProvider.languageModel(selectedChatModel),
      system: systemPrompt({ lang: cookieLocale, selectedChatModel }),
      messages,
      maxSteps: 10,
      experimental_activeTools: ['performRagQuery', 'getLeads', 'myNewTool'], // Добавьте имя вашего инструмента здесь
      experimental_generateMessageId: generateUUID,
      tools: {
        getLeads: {
          // ...существующий инструмент...
        },
        performRagQuery: performRagQuery({
          session,
          dataStream,
        }),
        // Добавьте ваш инструмент здесь
        myNewTool: myNewTool({
          session,
          dataStream, // Передайте dataStream для отображения прогресса
        }),
      },
      // ...остальная конфигурация...
    });
    // ...остальной код...
  },
});
```

### Шаг 3: Обновите список активных инструментов

Убедитесь, что вы добавили имя вашего инструмента в массив `experimental_activeTools`, чтобы LLM мог его использовать:

```typescript
experimental_activeTools: ['performRagQuery', 'getLeads', 'myNewTool'],
```

## Примеры инструментов

### Пример 1: Инструмент для поиска новостей

```typescript
// lib/ai/tools/get-news.ts
import { tool } from 'ai';
import { z } from 'zod';

export const getNews = tool({
  description: 'Get the latest news on a specific topic',
  parameters: z.object({
    topic: z.string().describe('The topic to search news for'),
    limit: z.number().default(5).describe('Number of news items to return'),
  }),
  execute: async ({ topic, limit }) => {
    try {
      const response = await fetch(
        `https://newsapi.org/v2/everything?q=${encodeURIComponent(topic)}&pageSize=${limit}&apiKey=${process.env.NEWS_API_KEY || 'demo-key'}`,
      );

      if (!response.ok) {
        throw new Error(`News API returned ${response.status}`);
      }

      const newsData = await response.json();
      return {
        articles: newsData.articles.map((article: any) => ({
          title: article.title,
          description: article.description,
          url: article.url,
          publishedAt: article.publishedAt,
          source: article.source.name,
        })),
        totalResults: newsData.totalResults,
      };
    } catch (error) {
      console.error('Error fetching news:', error);
      return {
        error: 'Failed to fetch news',
        message: error instanceof Error ? error.message : String(error),
      };
    }
  },
});
```

### Пример 2: Инструмент для анализа текста с отображением прогресса

```typescript
// lib/ai/tools/analyze-text-with-progress.ts
import { tool } from 'ai';
import { z } from 'zod';
import { DataStreamWriter } from 'ai';
import { generateUUID } from '@/lib/utils';

interface AnalyzeTextWithProgressProps {
  dataStream?: DataStreamWriter;
}

export const analyzeTextWithProgress = ({ dataStream }: AnalyzeTextWithProgressProps = {}) =>
  tool({
    description: 'Analyze text with detailed progress tracking',
    parameters: z.object({
      text: z.string().describe('The text to analyze'),
      includeEntities: z.boolean().default(true).describe('Include entity extraction'),
      includeSummary: z.boolean().default(true).describe('Include text summarization'),
    }),
    execute: async ({ text, includeEntities, includeSummary }) => {
      try {
        // Создаем шаги для отображения прогресса
        const steps = [
          {
            id: generateUUID(),
            name: 'Initialization',
            description: 'Preparing analysis environment',
            status: 'pending',
          },
          {
            id: generateUUID(),
            name: 'Sentiment Analysis',
            description: 'Analyzing text sentiment',
            status: 'pending',
          }
        ];
        
        if (includeEntities) {
          steps.push({
            id: generateUUID(),
            name: 'Entity Extraction',
            description: 'Identifying entities in text',
            status: 'pending',
          });
        }
        
        // Отправляем начальное состояние прогресса
        if (dataStream) {
          dataStream.writeData({
            type: 'text-delta',
            content: JSON.stringify({
              type: 'tool-progress',
              data: {
                steps,
                currentStepId: steps[0].id,
                isComplete: false,
                title: 'Text Analysis',
              }
            })
          });
        }
        
        // Функция для обновления прогресса
        const updateProgress = (stepIndex, updates) => {
          steps[stepIndex] = { ...steps[stepIndex], ...updates };
          
          if (dataStream) {
            dataStream.writeData({
              type: 'text-delta',
              content: JSON.stringify({
                type: 'tool-progress',
                data: {
                  steps,
                  currentStepId: updates.status === 'completed' ? 
                    (steps[stepIndex + 1]?.id || null) : steps[stepIndex].id,
                  isComplete: false,
                  title: 'Text Analysis',
                }
              })
            });
          }
        };
        
        // Реализация логики с обновлениями прогресса...
        
        return result;
      } catch (error) {
        // Обработка ошибок...
      }
    },
  });
```

### Пример 3: Инструмент для поиска по базе знаний

```typescript
// lib/ai/tools/search-knowledge-base.ts
import { tool } from 'ai';
import { z } from 'zod';
import { DataStreamWriter } from 'ai';
import type { Session } from 'next-auth';

interface SearchKnowledgeBaseProps {
  session: Session;
  dataStream?: DataStreamWriter;
}

export const searchKnowledgeBase = ({ session, dataStream }: SearchKnowledgeBaseProps) =>
  tool({
    description: 'Search the knowledge base for relevant information',
    parameters: z.object({
      query: z.string().describe('The search query'),
      filters: z
        .object({
          category: z.string().optional().describe('Filter by category'),
          dateFrom: z.string().optional().describe('Filter by date (from)'),
          dateTo: z.string().optional().describe('Filter by date (to)'),
        })
        .optional(),
      limit: z.number().default(5).describe('Maximum number of results'),
    }),
    execute: async ({ query, filters, limit }) => {
      if (!session?.user) {
        return {
          error: 'Unauthorized',
          message: 'User must be authenticated to search the knowledge base',
        };
      }

      try {
        if (dataStream) {
          dataStream.writeData({
            type: 'text-delta',
            content: JSON.stringify({
              type: 'tool-progress',
              data: {
                steps: [
                  {
                    id: '1',
                    name: 'Search',
                    description: `Searching for: "${query}"`,
                    status: 'in-progress',
                  }
                ],
                currentStepId: '1',
                isComplete: false,
                title: 'Knowledge Base Search',
              }
            })
          });
        }

        // Реализация поиска...
        
        return {
          results: searchResults,
          totalCount: searchResults.length,
          query,
          filters,
        };
      } catch (error) {
        // Обработка ошибок...
      }
    },
  });
```

## Заключение

При создании новых инструментов следуйте этим рекомендациям:

1. Используйте типизацию для параметров и результатов
2. Добавляйте подробные описания для параметров
3. Обрабатывайте ошибки и возвращайте информативные сообщения
4. Для сложных инструментов используйте отображение прогресса
5. Регистрируйте новые инструменты в файле `app/(chat)/api/chat/route.ts`

Следуя этому руководству, вы сможете создавать мощные инструменты, которые расширят возможности LLM-агента и улучшат пользовательский опыт.
