import { forbidden, notFound } from 'next/navigation';

import { auth } from '@/app/(auth)/auth';
import { Chat } from '@/components/chat';
import {
  getChatByAgentModel,
  getChatById,
  getMessagesByChatId,
  saveChat,
} from '@/lib/db/queries';
import { DataStreamHandler } from '@/components/data-stream-handler';
import { agentChatModels, MODERATOR_CHAT_MODEL } from '@/lib/ai/models';
import type { DBMessage, Chat as DBChat } from '@/lib/db/schema';
import type { Attachment, UIMessage } from 'ai';
import { generateUUID } from '@/lib/utils';
import { UserRole } from '@/app/(auth)/auth.config';
import { ClientAgentSetter } from '@/components/agent-setter';

export default async function Page(props: { params: Promise<{ id: string }> }) {
  const params = await props.params;
  const { id } = params;
  let chatId = id;

  const session = await auth();

  if (!session?.user?.id) {
    forbidden();
  }

  const agentChatModel =
    id === (UserRole.MODERATOR as string) && session?.user?.role === id
      ? MODERATOR_CHAT_MODEL
      : agentChatModels.find((agent) => agent.id === id);
  const newChatid = generateUUID();

  let chat: DBChat | null = null;
  if (agentChatModel && session?.user?.id) {
    try {
      chat = await getChatByAgentModel({
        agentModel: id,
        userId: session.user.id,
      });

      if (!chat) {
        chat = await saveChat({
          id: newChatid,
          userId: session.user.id,
          title: agentChatModel.name,
          agentModel: id,
        });

        if (!chat) {
          forbidden();
        }
      }

      chatId = chat.id;
    } catch (error) {
      chat = await getChatByAgentModel({
        agentModel: id,
        userId: session.user.id,
      });

      if (chat) {
        chatId = chat.id;
      } else {
        forbidden();
      }
    }
  }

  try {
    chat = await getChatById({ id: chatId });
  } catch (error) {
    notFound();
  }

  if (
    !chat ||
    (chat.userId !== session.user.id && session.user.role !== 'moderator')
  ) {
    notFound();
  }

  if (
    chat &&
    chat.agentModel === 'moderator' &&
    session?.user?.role !== 'moderator'
  ) {
    notFound();
  }

  const messagesFromDb = await getMessagesByChatId({
    id: chat.id,
  });

  const startAgent = messagesFromDb.length === 0;

  function convertToUIMessages(messages: Array<DBMessage>): Array<UIMessage> {
    return messages.map((message) => ({
      id: message.id,
      parts: message.parts as UIMessage['parts'],
      role: message.role as UIMessage['role'],
      // Note: content will soon be deprecated in @ai-sdk/react
      // @ts-ignore
      content: message.parts
        .filter((part) => part.type === 'text')
        .map((part) => part.text)
        .join('\n'),
      createdAt: message.createdAt,
      experimental_attachments:
        (message.attachments as Array<Attachment>) ?? [],
    }));
  }

  return (
    <>
      <ClientAgentSetter agent={chat.agentModel} />
      <Chat
        id={chat.id}
        initialMessages={convertToUIMessages(messagesFromDb)}
        selectedChatModel={chat.agentModel}
        isReadonly={session?.user?.id !== chat.userId}
        startAgent={startAgent}
      />
      <DataStreamHandler id={id} />
    </>
  );
}
