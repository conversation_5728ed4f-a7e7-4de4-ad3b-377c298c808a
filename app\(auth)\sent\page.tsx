'use client';

import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import HorizontalLanguageSwitcher from '@/components/hor-language-switcher';

export default function SentPage() {
  const searchParams = useSearchParams();
  const email = searchParams.get('email') || '';

  const t = useTranslations('auth');

  return (
    <div
      className="flex flex-col md:flex-row min-h-dvh overflow-y-auto w-screen items-start pt-0 md:items-center justify-center bg-primary bg-[url('/images/Background.png')] bg-no-repeat bg-cover bg-center"
      style={{
        backgroundSize: '110% 98%',
      }}
    >
      {/* Языковой переключатель */}
      <div className="absolute top-4 right-4 z-10">
        <HorizontalLanguageSwitcher />
      </div>
      <div className="w-full max-w-md rounded-lg border border-gray-200 bg-white p-6 shadow-md dark:border-gray-700 dark:bg-gray-800">
        <h1 className="mb-4 text-xl font-bold text-green-600 dark:text-green-500">
          {t('email_sent_title')}
        </h1>
        <div className="mb-4 rounded-md bg-green-50 p-4 dark:bg-green-900/20">
          <p className="text-sm font-medium text-gray-800 dark:text-gray-200">
            {t('email_sent_success_title')}
          </p>
          <p className="mt-2 text-sm text-gray-600 dark:text-gray-400 font-bold">
            {t('email_sent_description', { email })}
          </p>
          <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
            {t('email_sent_check_spam')}
          </p>
        </div>
        <div className="flex">
          <Link
            href="/"
            className="inline-flex w-full items-center justify-center rounded-md bg-gray-100 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600"
          >
            {t('back_to_home')}
          </Link>
        </div>
      </div>
    </div>
  );
}
