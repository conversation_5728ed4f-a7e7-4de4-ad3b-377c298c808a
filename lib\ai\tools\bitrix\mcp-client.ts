import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { SSEClientTransport } from '@modelcontextprotocol/sdk/client/sse.js';

// Задаем значения напрямую, если переменные окружения не доступны
export const mcpServerUrl =
  process.env.MCP_SERVER_URL ||
  'https://remote-mcp-server-authless.alonalx.workers.dev/sse';
export const bitrixDomain =
  process.env.BITRIX_DOMAIN || 'b24-xxu92p.bitrix24.com';
export const bitrixWebhookToken =
  process.env.BITRIX_WEBHOOK_TOKEN || '1/hyp4d13kixaq5r3k';

console.log(`[DEBUG] MCP_SERVER_URL: ${mcpServerUrl}`);
console.log(`[DEBUG] BITRIX_DOMAIN: ${bitrixDomain}`);
console.log(
  `[DEBUG] BITRIX_WEBHOOK_TOKEN: ${bitrixWebhookToken ? '***' : 'not set'}`,
);

if (!mcpServerUrl) {
  console.warn(
    'MCP_SERVER_URL environment variable is not set. Bitrix24 integration will be disabled.',
  );
}

let mcpClient: Client | null = null;

// Инициализация MCP клиента
export async function initMcpClient(): Promise<Client | null> {
  if (mcpClient) {
    return mcpClient;
  }

  if (!mcpServerUrl) {
    return null;
  }

  try {
    // Создаем клиент с параметрами
    mcpClient = new Client({
      name: 'agent-mcp-client',
      version: '1.0.0',
    });

    console.log('[MCP Client] Initialized client with name: agent-mcp-client');

    // Создаем SSE транспорт для подключения к MCP серверу
    // Используем объект URL вместо строки
    const serverUrl = new URL(mcpServerUrl);
    const transport = new SSEClientTransport(serverUrl);

    // Подключаемся к MCP серверу с использованием транспорта
    await mcpClient.connect(transport);

    console.log(
      `[MCP Client] Successfully connected to MCP server: ${mcpServerUrl}`,
    );

    if (bitrixDomain && bitrixWebhookToken) {
      console.log(
        `[MCP Client] Bitrix24 credentials available: ${bitrixDomain}`,
      );
    }

    return mcpClient;
  } catch (error) {
    console.error(`[MCP Client] Failed to initialize or connect: ${error}`);
    mcpClient = null;
    return null;
  }
}

// Получение экземпляра MCP клиента
export function getMcpClient(): Client | null {
  return mcpClient;
}

// Тип для ответа MCP
export type MCPResponse = {
  content?: Array<{ type: string; text?: string }>;
  isError?: boolean;
  [key: string]: unknown;
};

// Функция для вызова инструмента MCP с таймаутом
export async function callMcpTool(
  toolName: string,
  args: Record<string, unknown>,
  timeoutMs = 10000,
) {
  if (!mcpClient) {
    throw new Error('MCP client not configured or not connected');
  }

  // Добавляем таймаут для запроса, чтобы избежать ошибки связи
  const timeoutPromise = new Promise<never>((_, reject) => {
    setTimeout(
      () =>
        reject(
          new Error(`Timeout waiting for MCP response after ${timeoutMs}ms`),
        ),
      timeoutMs,
    );
  });

  const toolCallPromise = mcpClient.callTool({
    name: toolName,
    arguments: args,
  });

  // Используем Promise.race для ограничения времени запроса
  return Promise.race([toolCallPromise, timeoutPromise]);
}

/**
 * Обрабатывает ответ от MCP инструмента
 * @param response - Ответ от MCP инструмента
 * @returns Объект с результатом обработки
 */
export function processMcpResponse(response: unknown): {
  success: boolean;
  data?: string;
  error?: string;
} {
  const typedResponse = response as MCPResponse;
  const resultText = Array.isArray(typedResponse?.content)
    ? typedResponse.content.find((c) => c.type === 'text')?.text
    : undefined;

  if (typedResponse?.isError || resultText?.toLowerCase().includes('ошибка')) {
    const errorMessage = `Bitrix24 MCP Error: ${resultText || 'Unknown error from MCP server'}`;
    console.error(`[MCP Client] Error response: ${errorMessage}`);
    return { success: false, error: errorMessage };
  }

  return {
    success: true,
    data: resultText || 'Получен пустой ответ от Bitrix24',
  };
}

// Инициализируем клиент при импорте модуля
initMcpClient().catch((error) => {
  console.error('[MCP Client] Failed to initialize on module import:', error);
});
