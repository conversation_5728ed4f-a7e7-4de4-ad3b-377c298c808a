// lib/rag/vectorDB/operations.pgvec.ts
import {desc, gt, cosineDistance, sql, AnyColumn} from 'drizzle-orm';
import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import {generateEmbedding} from "@/lib/rag/utils/generateEmbeddings";
import {config} from "dotenv";
config({
    path: '.env.local',
});
import {
    ragDocument,
} from './rag.schema';

// biome-ignore lint: Forbidden non-null assertion.
import fs from 'fs';
import path from 'path';
// Определение директории для документов
const DOCUMENTS_DIR = path.join(__dirname, 'documents');

// biome-ignore lint: Forbidden non-null assertion.
const client = postgres(process.env.POSTGRES_URL!);
const db = drizzle(client);

export async function findSimilarDocs (description: string) {
    const embedding = await generateEmbedding(description);
    const similarity = sql<number>`1 - (${cosineDistance(ragDocument.embedding, embedding)})`;
    const similarDocs = await db
        .select({ file_name: ragDocument.fileName, content: ragDocument.content, similarity })
        .from(ragDocument)
        .where(gt(similarity, 0.5))
        .orderBy((t) => desc(t.similarity))
        .limit(4);
    return similarDocs;
}

export async function pgvectorProcessdoc() {
    if (!process.env.POSTGRES_URL) {
        throw new Error('POSTGRES_URL is not defined');
    }

    const files = fs.readdirSync(DOCUMENTS_DIR).filter(file => file.endsWith('.txt'));

    for (const file of files) {
        const filePath = path.join(DOCUMENTS_DIR, file);
        const content = fs.readFileSync(filePath, 'utf-8');

        const embedding = await generateEmbedding(content);

        await db.insert(ragDocument).values({
            fileName: file,
            content: content,
            embedding: embedding,
        });

        console.log(`Документ сохранен: ${file}`);
    }
}
export function uploadDocuments(sourceDir: string) {
    if (!fs.existsSync(sourceDir)&& !fs.existsSync(DOCUMENTS_DIR)) {
        fs.mkdirSync(sourceDir);
        fs.mkdirSync(DOCUMENTS_DIR);
    }
    const files = fs.readdirSync(sourceDir);

    files.forEach(file => {
        const sourceFilePath = path.join(sourceDir, file);
        const destinationFilePath = path.join(DOCUMENTS_DIR, file);

        fs.renameSync(sourceFilePath, destinationFilePath);
        console.log(`Файл перемещен: ${sourceFilePath} -> ${destinationFilePath}`);
    });
}
