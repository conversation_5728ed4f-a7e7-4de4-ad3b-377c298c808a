import('server-only');
// @ts-ignore
import { genSaltSync, hashSync } from 'bcrypt-ts';
import {
  and,
  asc,
  desc,
  eq,
  gt,
  gte,
  inArray,
  lte,
  sql,
  count,
} from 'drizzle-orm';
import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
// @ts-ignore
import {
  user,
  chat,
  type User,
  document,
  type Suggestion,
  suggestion,
  message,
  vote,
  type DBMessage,
  order,
  services,
  type OrderRecipient,
  type OrderItem,
  orderItem,
} from './schema';
import type { ArtifactKind } from '@/components/artifact';

// Optionally, if not using email/pass login, you can
// use the Drizzle adapter for Auth.js / NextAuth
// https://authjs.dev/reference/adapter/drizzle
import { config } from 'dotenv';
config({
  path: '.env.local',
});

// @ts-ignore
// biome-ignore lint/style/noNonNullAssertion: <explanation>
const client = postgres(process.env.POSTGRES_URL!);
export const db = drizzle(client);

export async function getUser(email: string): Promise<Array<User>> {
  try {
    // @ts-ignore
    return await db.select().from(user).where(eq(user.email, email));
  } catch (error) {
    console.error('Failed to get user from database');
    throw error;
  }
}

export async function createUser(
  email: string,
  password: string,
  name?: string,
  image?: string,
) {
  const salt = genSaltSync(10);
  const hash = hashSync(password, salt);

  try {
    // @ts-ignore
    return await db.insert(user).values({ email, password: hash, name, image });
  } catch (error) {
    console.error('Failed to create user in database');
    throw error;
  }
}

export async function createAdmin(
  email: string,
  password: string,
  name?: string,
  image?: string,
) {
  const salt = genSaltSync(10);
  const hash = hashSync(password, salt);

  try {
    // @ts-ignore
    return await db
      .insert(user)
      .values({ email, password: hash, name, image, role: 'admin' });
  } catch (error) {
    console.error('Failed to create admin in database');
    throw error;
  }
}

export async function updateUser({
  id,
  name,
  image,
}: {
  id: string;
  name?: string;
  image?: string;
}) {
  try {
    // @ts-ignore
    return await db.update(user).set({ name, image }).where(eq(user.id, id));
  } catch (error) {
    console.error('Failed to update user in database');
    throw error;
  }
}

export async function saveChat({
  id,
  userId,
  title,
  agentModel,
}: {
  id: string;
  userId: string;
  title: string;
  agentModel: string;
}) {
  try {
    // Используем транзакцию для атомарности операций
    return await db.transaction(async (tx) => {
      // Сначала проверяем, существует ли уже чат с таким agentModel
      const existingChat = await tx
        .select()
        .from(chat)
        .where(and(eq(chat.agentModel, agentModel), eq(chat.userId, userId)))
        .limit(1)
        .then((rows) => rows[0] || null);

      if (existingChat) {
        return existingChat;
      }

      // Если чат не существует, создаем новый
      await tx.insert(chat).values({
        id,
        createdAt: new Date(),
        userId,
        title,
        agentModel,
      });

      return await tx
        .select()
        .from(chat)
        .where(eq(chat.id, id))
        .limit(1)
        .then((rows) => rows[0]);
    });
  } catch (error) {
    if (error instanceof Error && error.message.includes('unique constraint')) {
      return Promise.resolve(null);
    }

    console.error('Failed to save chat in database:', error);
    throw error;
  }
}

export async function deleteChatById({
  id,
  onlyClearMessages = false,
}: { id: string; onlyClearMessages?: boolean }) {
  try {
    await db.delete(vote).where(eq(vote.chatId, id));
    await db.delete(message).where(eq(message.chatId, id));

    if (onlyClearMessages) {
      return Promise.resolve(true);
    }

    return await db.delete(chat).where(eq(chat.id, id));
  } catch (error) {
    console.error('Failed to delete chat by id from database');
    throw error;
  }
}

export async function getChatsByUserId({ id }: { id: string }) {
  try {
    return await db
      .select()
      .from(chat)
      .where(eq(chat.userId, id))
      .orderBy(desc(chat.createdAt));
  } catch (error) {
    console.error('Failed to get chats by user from database');
    throw error;
  }
}

export async function getChatById({ id }: { id: string }) {
  try {
    const [selectedChat] = await db.select().from(chat).where(eq(chat.id, id));
    return selectedChat;
  } catch (error) {
    console.error('Failed to get chat by id from database');
    throw error;
  }
}

export async function getChatByAgentModel({
  agentModel,
  userId,
}: { agentModel: string; userId: string }) {
  try {
    const [selectedChat] = await db
      .select()
      .from(chat)
      .where(and(eq(chat.agentModel, agentModel), eq(chat.userId, userId)));
    return selectedChat;
  } catch (error) {
    console.error('Failed to get chat by id from database');
    throw error;
  }
}

export async function saveMessages({
  messages,
}: {
  messages: Array<DBMessage>;
}) {
  try {
    return await db.insert(message).values(messages);
  } catch (error) {
    console.error('Failed to save messages in database', error);
    throw error;
  }
}

export async function getMessagesByChatId({ id }: { id: string }) {
  try {
    return await db
      .select()
      .from(message)
      .where(eq(message.chatId, id))
      .orderBy(asc(message.createdAt));
  } catch (error) {
    console.error('Failed to get messages by chat id from database', error);
    throw error;
  }
}

export async function voteMessage({
  chatId,
  messageId,
  type,
}: {
  chatId: string;
  messageId: string;
  type: 'up' | 'down';
}) {
  try {
    const [existingVote] = await db
      .select()
      .from(vote)
      .where(and(eq(vote.messageId, messageId)));

    if (existingVote) {
      return await db
        .update(vote)
        .set({ isUpvoted: type === 'up' })
        .where(and(eq(vote.messageId, messageId), eq(vote.chatId, chatId)));
    }
    return await db.insert(vote).values({
      chatId,
      messageId,
      isUpvoted: type === 'up',
    });
  } catch (error) {
    console.error('Failed to upvote message in database', error);
    throw error;
  }
}

export async function getVotesByChatId({ id }: { id: string }) {
  try {
    return await db.select().from(vote).where(eq(vote.chatId, id));
  } catch (error) {
    console.error('Failed to get votes by chat id from database', error);
    throw error;
  }
}

export async function saveDocument({
  id,
  title,
  kind,
  content,
  userId,
}: {
  id: string;
  title: string;
  kind: ArtifactKind;
  content: string;
  userId: string;
}) {
  try {
    return await db.insert(document).values({
      id,
      title,
      kind,
      content,
      userId,
      createdAt: new Date(),
    });
  } catch (error) {
    console.error('Failed to save document in database');
    throw error;
  }
}

export async function getDocumentsById({ id }: { id: string }) {
  try {
    const documents = await db
      .select()
      .from(document)
      .where(eq(document.id, id))
      .orderBy(asc(document.createdAt));

    return documents;
  } catch (error) {
    console.error('Failed to get document by id from database');
    throw error;
  }
}

export async function getDocumentById({ id }: { id: string }) {
  try {
    const [selectedDocument] = await db
      .select()
      .from(document)
      .where(eq(document.id, id))
      .orderBy(desc(document.createdAt));

    return selectedDocument;
  } catch (error) {
    console.error('Failed to get document by id from database');
    throw error;
  }
}

export async function deleteDocumentsByIdAfterTimestamp({
  id,
  timestamp,
}: {
  id: string;
  timestamp: Date;
}) {
  try {
    await db
      .delete(suggestion)
      .where(
        and(
          eq(suggestion.documentId, id),
          gt(suggestion.documentCreatedAt, timestamp),
        ),
      );

    return await db
      .delete(document)
      // @ts-ignore
      .where(and(eq(document.id, id), gt(document.createdAt, timestamp)));
  } catch (error) {
    console.error(
      'Failed to delete documents by id after timestamp from database',
    );
    throw error;
  }
}

export async function saveSuggestions({
  suggestions,
}: {
  suggestions: Array<Suggestion>;
}) {
  try {
    return await db.insert(suggestion).values(suggestions);
  } catch (error) {
    console.error('Failed to save suggestions in database');
    throw error;
  }
}

export async function getSuggestionsByDocumentId({
  documentId,
}: {
  documentId: string;
}) {
  try {
    return await db
      .select()
      .from(suggestion)
      .where(and(eq(suggestion.documentId, documentId)));
  } catch (error) {
    console.error(
      'Failed to get suggestions by document version from database',
    );
    throw error;
  }
}

export async function getMessageById({ id }: { id: string }) {
  try {
    return await db.select().from(message).where(eq(message.id, id));
  } catch (error) {
    console.error('Failed to get message by id from database');
    throw error;
  }
}

export async function deleteMessagesByChatIdAfterTimestamp({
  chatId,
  timestamp,
}: {
  chatId: string;
  timestamp: Date;
}) {
  try {
    const messagesToDelete = await db
      .select({ id: message.id })
      .from(message)
      .where(
        and(eq(message.chatId, chatId), gte(message.createdAt, timestamp)),
      );

    const messageIds = messagesToDelete.map((message) => message.id);

    if (messageIds.length > 0) {
      await db
        .delete(vote)
        .where(
          and(eq(vote.chatId, chatId), inArray(vote.messageId, messageIds)),
        );

      return await db
        .delete(message)
        .where(
          and(eq(message.chatId, chatId), inArray(message.id, messageIds)),
        );
    }
  } catch (error) {
    console.error(
      'Failed to delete messages by id after timestamp from database',
    );
    throw error;
  }
}

export async function updateChatVisiblityById({
  chatId,
  visibility,
}: {
  chatId: string;
  visibility: 'private' | 'public';
}) {
  try {
    return await db.update(chat).set({ visibility }).where(eq(chat.id, chatId));
  } catch (error) {
    console.error('Failed to update chat visibility in database');
    throw error;
  }
}

// ===== ORDER MANAGEMENT =====

export async function createOrder({
  recipient,
}: {
  recipient: OrderRecipient;
}) {
  try {
    const [newOrder] = await db
      .insert(order)
      .values({
        recipient,
        status: 'new',
      })
      .returning();
    return newOrder;
  } catch (error) {
    console.error('Failed to create order:', error);
    throw error;
  }
}

export async function createOrderWithItems({
  recipient,
  items,
}: {
  recipient: OrderRecipient;
  items: Omit<OrderItem, 'id' | 'orderId'>[];
}) {
  try {
    // Создаем заказ
    const [newOrder] = await db
      .insert(order)
      .values({
        recipient,
        status: 'new',
      })
      .returning();

    // Создаем элементы заказа
    if (items.length > 0) {
      const orderItems = items.map((item) => ({
        ...item,
        orderId: newOrder.id,
      }));

      await db.insert(orderItem).values(orderItems);
    }

    return newOrder;
  } catch (error) {
    console.error('Failed to create order with items:', error);
    throw error;
  }
}

export async function getOrderById({ id }: { id: number }) {
  try {
    const [orderData] = await db.select().from(order).where(eq(order.id, id));
    return orderData;
  } catch (error) {
    console.error('Failed to get order by id:', error);
    throw error;
  }
}

export async function getAllOrders({
  limit = 1,
  offset = 0,
  filters = {},
  sortBy = 'dateCreated',
  sortOrder = 'desc',
}: {
  limit?: number;
  offset?: number;
  filters?: {
    status?: 'new' | 'approved';
    dateFrom?: Date;
    dateTo?: Date;
    recipientEmail?: string;
    recipientName?: string;
    recipientSecondName?: string;
    recipientLastName?: string;
    recipientPhone?: string;
  };
  sortBy?: 'dateCreated' | 'dateUpdated' | 'status' | 'id';
  sortOrder?: 'asc' | 'desc';
} = {}) {
  try {
    // Проверяем подключение к базе данных
    try {
      const testResult = await db.select({ test: sql`1` });
    } catch (dbError) {
      console.error('[getAllOrders] Database connection failed:', dbError);
      throw new Error(
        `Database connection failed: ${dbError instanceof Error ? dbError.message : 'Unknown database error'}`,
      );
    }

    // Строим условия фильтрации
    const conditions: any[] = [];

    if (filters.status) {
      conditions.push(eq(order.status, filters.status));
    }

    if (filters.dateFrom) {
      conditions.push(gte(order.dateCreated, filters.dateFrom));
    }

    if (filters.dateTo) {
      conditions.push(lte(order.dateCreated, filters.dateTo));
    }

    if (filters.recipientEmail) {
      // Поиск по email в JSON поле recipient
      conditions.push(
        sql`${order.recipient}->>'email' ILIKE ${`%${filters.recipientEmail}%`}`,
      );
    }

    if (filters.recipientName) {
      // Поиск по имени в JSON поле recipient
      conditions.push(
        sql`${order.recipient}->>'name' ILIKE ${`%${filters.recipientName}%`}`,
      );
    }

    if (filters.recipientSecondName) {
      // Поиск по отчеству в JSON поле recipient
      conditions.push(
        sql`${order.recipient}->>'secondName' ILIKE ${`%${filters.recipientSecondName}%`}`,
      );
    }

    if (filters.recipientLastName) {
      // Поиск по фамилии в JSON поле recipient
      conditions.push(
        sql`${order.recipient}->>'lastName' ILIKE ${`%${filters.recipientLastName}%`}`,
      );
    }

    if (filters.recipientPhone) {
      // Поиск по телефону в JSON поле recipient
      conditions.push(
        sql`${order.recipient}->>'phone' ILIKE ${`%${filters.recipientPhone}%`}`,
      );
    }

    const whereClause = conditions.length > 0 ? and(...conditions) : undefined;

    // Определяем поле и направление сортировки
    const sortField = {
      dateCreated: order.dateCreated,
      dateUpdated: order.dateUpdated,
      status: order.status,
      id: order.id,
    }[sortBy];

    if (!sortField) {
      throw new Error(
        `Invalid sortBy field: ${sortBy}. Allowed fields: dateCreated, dateUpdated, status, id`,
      );
    }

    const sortDirection =
      sortOrder === 'asc' ? asc(sortField) : desc(sortField);

    // Получаем общее количество записей с учетом фильтрации
    const [countResult] = await db
      .select({ count: count() })
      .from(order)
      .where(whereClause);

    // Получаем отфильтрованные и отсортированные записи
    const orders = await db
      .select()
      .from(order)
      .where(whereClause)
      .orderBy(sortDirection)
      .limit(limit)
      .offset(offset);

    const result = {
      orders,
      count_all: countResult.count,
      pagination: {
        limit,
        offset,
        total: countResult.count,
        hasMore: offset + limit < countResult.count,
      },
    };

    return result;
  } catch (error) {
    console.error(
      '[getAllOrders] Error stack:',
      error instanceof Error ? error.stack : 'No stack trace',
    );
    throw error;
  }
}

export async function getOrdersByStatus({
  status,
  limit = 50,
  offset = 0,
}: {
  status: 'new' | 'approved';
  limit?: number;
  offset?: number;
}) {
  try {
    return await db
      .select()
      .from(order)
      .where(eq(order.status, status))
      .orderBy(desc(order.dateCreated))
      .limit(limit)
      .offset(offset);
  } catch (error) {
    console.error('Failed to get orders by status:', error);
    throw error;
  }
}

export async function updateOrderStatus({
  id,
  status,
}: {
  id: number;
  status: 'new' | 'approved';
}) {
  try {
    const [updatedOrder] = await db
      .update(order)
      .set({
        status,
        dateUpdated: new Date(),
      })
      .where(eq(order.id, id))
      .returning();
    return updatedOrder;
  } catch (error) {
    console.error('Failed to update order status:', error);
    throw error;
  }
}

export async function updateOrder({
  id,
  recipient,
}: {
  id: number;
  recipient?: OrderRecipient;
}) {
  try {
    const updateData: any = {
      dateUpdated: new Date(),
    };

    if (recipient !== undefined) updateData.recipient = recipient;

    const [updatedOrder] = await db
      .update(order)
      .set(updateData)
      .where(eq(order.id, id))
      .returning();
    return updatedOrder;
  } catch (error) {
    console.error('Failed to update order:', error);
    throw error;
  }
}

// ===== ORDER ITEMS MANAGEMENT =====

export async function createOrderItem({
  orderId,
  serviceId,
  title,
  agentModel,
  quantity,
  price,
  currency,
}: {
  orderId: number;
  serviceId: string;
  title: string;
  agentModel: string;
  quantity: number;
  price: string;
  currency: string;
}) {
  try {
    const [newOrderItem] = await db
      .insert(orderItem)
      .values({
        orderId,
        serviceId,
        title,
        agentModel,
        quantity,
        price,
        currency,
      })
      .returning();
    return newOrderItem;
  } catch (error) {
    console.error('Failed to create order item:', error);
    throw error;
  }
}

export async function getOrderItemsByOrderId({ orderId }: { orderId: number }) {
  try {
    return await db
      .select()
      .from(orderItem)
      .where(eq(orderItem.orderId, orderId));
  } catch (error) {
    console.error('Failed to get order items by order id:', error);
    throw error;
  }
}

export async function getOrderWithItems({ id }: { id: number }) {
  try {
    const orderData = await getOrderById({ id });
    if (!orderData) return null;

    const items = await getOrderItemsByOrderId({ orderId: id });

    return {
      ...orderData,
      items,
    };
  } catch (error) {
    console.error('Failed to get order with items:', error);
    throw error;
  }
}

export async function updateOrderItem({
  id,
  serviceId,
  title,
  agentModel,
  quantity,
  price,
  currency,
}: {
  id: string;
  serviceId?: string;
  title?: string;
  agentModel?: string;
  quantity?: number;
  price?: string;
  currency?: string;
}) {
  try {
    const updateData: any = {};

    if (serviceId !== undefined) updateData.serviceId = serviceId;
    if (title !== undefined) updateData.title = title;
    if (agentModel !== undefined) updateData.agentModel = agentModel;
    if (quantity !== undefined) updateData.quantity = quantity;
    if (price !== undefined) updateData.price = price;
    if (currency !== undefined) updateData.currency = currency;

    const [updatedOrderItem] = await db
      .update(orderItem)
      .set(updateData)
      .where(eq(orderItem.id, id))
      .returning();
    return updatedOrderItem;
  } catch (error) {
    console.error('Failed to update order item:', error);
    throw error;
  }
}

export async function deleteOrderItem({ id }: { id: string }) {
  try {
    const [deletedOrderItem] = await db
      .delete(orderItem)
      .where(eq(orderItem.id, id))
      .returning();
    return deletedOrderItem;
  } catch (error) {
    console.error('Failed to delete order item:', error);
    throw error;
  }
}

// ===== SERVICES MANAGEMENT =====

export async function createService({
  referenceId,
  title,
  cost,
  currency,
  requiredPrepaid = false,
  prepaidAmount = '0',
}: {
  referenceId: string;
  title: string;
  cost: string;
  currency: string;
  requiredPrepaid?: boolean;
  prepaidAmount?: string;
}) {
  try {
    const [newService] = await db
      .insert(services)
      .values({
        referenceId,
        title,
        cost,
        currency,
        requiredPrepaid,
        prepaidAmount,
      })
      .returning();
    return newService;
  } catch (error) {
    console.error('Failed to create service:', error);
    throw error;
  }
}

export async function getServiceById({ id }: { id: string }) {
  try {
    const [service] = await db
      .select()
      .from(services)
      .where(eq(services.id, id));
    return service;
  } catch (error) {
    console.error('Failed to get service by id:', error);
    throw error;
  }
}

export async function getServiceByReferenceId({
  referenceId,
}: { referenceId: string }) {
  try {
    const [service] = await db
      .select()
      .from(services)
      .where(eq(services.referenceId, referenceId));
    return service;
  } catch (error) {
    console.error('Failed to get service by reference id:', error);
    throw error;
  }
}

export async function getAllServices({
  limit = 100,
  offset = 0,
}: {
  limit?: number;
  offset?: number;
} = {}) {
  try {
    return await db
      .select()
      .from(services)
      .orderBy(asc(services.title))
      .limit(limit)
      .offset(offset);
  } catch (error) {
    console.error('Failed to get all services:', error);
    throw error;
  }
}

export async function getServicesByReferenceIds({
  referenceIds,
}: {
  referenceIds: string[];
}) {
  try {
    return await db
      .select()
      .from(services)
      .where(inArray(services.referenceId, referenceIds));
  } catch (error) {
    console.error('Failed to get services by reference ids:', error);
    throw error;
  }
}

export async function updateService({
  id,
  title,
  cost,
  currency,
  requiredPrepaid,
  prepaidAmount,
}: {
  id: string;
  title?: string;
  cost?: string;
  currency?: string;
  requiredPrepaid?: boolean;
  prepaidAmount?: string;
}) {
  try {
    const updateData: any = {};

    if (title !== undefined) updateData.title = title;
    if (cost !== undefined) updateData.cost = cost;
    if (currency !== undefined) updateData.currency = currency;
    if (requiredPrepaid !== undefined)
      updateData.requiredPrepaid = requiredPrepaid;
    if (prepaidAmount !== undefined) updateData.prepaidAmount = prepaidAmount;

    const [updatedService] = await db
      .update(services)
      .set(updateData)
      .where(eq(services.id, id))
      .returning();
    return updatedService;
  } catch (error) {
    console.error('Failed to update service:', error);
    throw error;
  }
}

export async function deleteService({ id }: { id: string }) {
  try {
    const [deletedService] = await db
      .delete(services)
      .where(eq(services.id, id))
      .returning();
    return deletedService;
  } catch (error) {
    console.error('Failed to delete service:', error);
    throw error;
  }
}
