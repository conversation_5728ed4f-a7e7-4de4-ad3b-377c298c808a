import type {
  CoreAssistantMessage,
  CoreToolMessage,
  Message,
  TextStreamPart,
  ToolInvocation,
  ToolSet,
  UIMessage,
} from 'ai';
import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

import type { DBMessage, Document } from '@/lib/db/schema';
import { useTranslations } from 'next-intl';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

interface ApplicationError extends Error {
  info: string;
  status: number;
}

export const fetcher = async (url: string) => {
  const res = await fetch(url);

  if (!res.ok) {
    const error = new Error(
      'An error occurred while fetching the data.',
    ) as ApplicationError;

    error.info = await res.json();
    error.status = res.status;

    throw error;
  }

  return res.json();
};

export function getLocalStorage(key: string) {
  if (typeof window !== 'undefined') {
    return JSON.parse(localStorage.getItem(key) || '[]');
  }
  return [];
}

export function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    const r = (Math.random() * 16) | 0;
    const v = c === 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}

function addToolMessageToChat({
  toolMessage,
  messages,
}: {
  toolMessage: CoreToolMessage;
  messages: Array<Message>;
}): Array<Message> {
  return messages.map((message) => {
    if (message.toolInvocations) {
      return {
        ...message,
        toolInvocations: message.toolInvocations.map((toolInvocation) => {
          const toolResult = toolMessage.content.find(
            (tool) => tool.toolCallId === toolInvocation.toolCallId,
          );

          if (toolResult) {
            return {
              ...toolInvocation,
              state: 'result',
              result: toolResult.result,
            };
          }

          return toolInvocation;
        }),
      };
    }

    return message;
  });
}

type ResponseMessageWithoutId = CoreToolMessage | CoreAssistantMessage;
type ResponseMessage = ResponseMessageWithoutId & { id: string };

export function sanitizeResponseMessages({
  messages,
  reasoning,
}: {
  messages: Array<ResponseMessage>;
  reasoning: string | undefined;
}) {
  const toolResultIds: Array<string> = [];

  for (const message of messages) {
    if (message.role === 'tool') {
      for (const content of message.content) {
        if (content.type === 'tool-result') {
          toolResultIds.push(content.toolCallId);
        }
      }
    }
  }

  const messagesBySanitizedContent = messages.map((message) => {
    if (message.role !== 'assistant') return message;

    if (typeof message.content === 'string') return message;

    const sanitizedContent = message.content.filter((content) =>
      content.type === 'tool-call'
        ? toolResultIds.includes(content.toolCallId)
        : content.type === 'text'
          ? content.text.length > 0
          : true,
    );

    if (reasoning) {
      // @ts-expect-error: reasoning message parts in sdk is wip
      sanitizedContent.push({ type: 'reasoning', reasoning });
    }

    return {
      ...message,
      content: sanitizedContent,
    };
  });

  return messagesBySanitizedContent.filter(
    (message) => message.content.length > 0,
  );
}

export function getMostRecentUserMessage(messages: Array<UIMessage>) {
  const userMessages = messages.filter((message) => message.role === 'user');
  return userMessages.at(-1);
}

export function getDocumentTimestampByIndex(
  documents: Array<Document>,
  index: number,
) {
  if (!documents) return new Date();
  if (index > documents.length) return new Date();

  return documents[index].createdAt;
}

export function getTrailingMessageId({
  messages,
}: {
  messages: Array<ResponseMessage>;
}): string | null {
  const trailingMessage = messages.at(-1);

  if (!trailingMessage) return null;

  return trailingMessage.id;
}

export function getAuthErrorMessageByCode(errorCode: string | null) {
  // Используйте переводы из файлов локализации
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const t = useTranslations('auth_errors');

  if (!errorCode) {
    return {
      title: t('unknown_error.title'),
      description: t('unknown_error.description'),
    };
  }

  switch (errorCode) {
    // Ошибки входа через учетные данные
    case 'CredentialsSignin':
      return {
        title: t('credentials_signin.title'),
        description: t('credentials_signin.description'),
      };

    // Ошибки доступа
    case 'AccessDenied':
      return {
        title: t('access_denied.title'),
        description: t('access_denied.description'),
      };

    // Ошибки OAuth
    case 'OAuthAccountNotLinked':
      return {
        title: t('oauth_account_not_linked.title'),
        description: t('oauth_account_not_linked.description'),
      };
    case 'OAuthCallbackError':
      return {
        title: t('oauth_callback_error.title'),
        description: t('oauth_callback_error.description'),
      };
    case 'OAuthSignInError':
      return {
        title: t('oauth_signin_error.title'),
        description: t('oauth_signin_error.description'),
      };

    // Ошибки Email аутентификации
    case 'EmailSignInError':
      return {
        title: t('email_signin_error.title'),
        description: t('email_signin_error.description'),
      };
    case 'Verification':
      return {
        title: t('verification.title'),
        description: t('verification.description'),
      };

    // Ошибки WebAuthn
    case 'WebAuthnVerificationError':
      return {
        title: t('webauthn_verification_error.title'),
        description: t('webauthn_verification_error.description'),
      };

    // Ошибки связывания аккаунтов
    case 'AccountNotLinked':
      return {
        title: t('account_not_linked.title'),
        description: t('account_not_linked.description'),
      };

    // Ошибки CSRF
    case 'MissingCSRF':
      return {
        title: t('missing_csrf.title'),
        description: t('missing_csrf.description'),
      };

    // Общие ошибки
    case 'CallbackRouteError':
      return {
        title: t('callback_route_error.title'),
        description: t('callback_route_error.description'),
      };
    case 'SessionTokenError':
      return {
        title: t('session_token_error.title'),
        description: t('session_token_error.description'),
      };
    case 'SignOutError':
      return {
        title: t('signout_error.title'),
        description: t('signout_error.description'),
      };

    // Ошибки конфигурации
    case 'MissingAdapter':
    case 'MissingAdapterMethods':
    case 'MissingAuthorize':
    case 'MissingSecret':
    case 'UnsupportedStrategy':
    case 'InvalidProvider':
    case 'UntrustedHost':
    case 'InvalidEndpoints':
    case 'InvalidCheck':
    case 'JWTSessionError':
    case 'AdapterError':
    case 'ErrorPageLoop':
    case 'EventError':
    case 'InvalidCallbackUrl':
    case 'OAuthProfileParseError':
    case 'UnknownAction':
    case 'DuplicateConditionalUI':
    case 'MissingWebAuthnAutocomplete':
    case 'ExperimentalFeatureNotEnabled':
      return {
        title: t('configuration_error.title'),
        description: t('configuration_error.description'),
      };

    default:
      return {
        title: `${t('unknown_error.title')}: ${errorCode}`,
        description: t('unknown_error.description'),
      };
  }
}

export function isTimeDifferenceGreaterThanDays(createdAt: Date|undefined, daysThreshold: number): boolean {
  if (!createdAt) {
    return false;
  }
  const createdDate = new Date(createdAt);
  const currentDate = new Date();

  // Разница в миллисекундах
  const differenceMs = currentDate.getTime() - createdDate.getTime();

  // Переводим дни в миллисекунды и сравниваем
  const thresholdMs = daysThreshold * 24 * 60 * 60 * 1000;

  return differenceMs > thresholdMs;
}