// lib/rag/utils/generateEmbeddings.ts
let extractor: any = null;

export async function generateEmbedding(text: string): Promise<number[]> {
    if (!extractor) {
        const { pipeline } = await import('@xenova/transformers');
        extractor = await pipeline('feature-extraction', 'Xenova/all-MiniLM-L6-v2');
    }

    const output = await extractor(text, { pooling: 'mean', normalize: true });
    return Array.from(output.data); // массив чисел
}
