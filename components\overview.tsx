'use client';

import { motion } from 'framer-motion';
import { useTranslations } from 'next-intl';
import { agentChatModels, MODERATOR_CHAT_MODEL } from '@/lib/ai/models';

export const Overview = ({
  selectedChatModel,
}: { selectedChatModel: string }) => {
  const t = useTranslations('overview');
  const selectedAgent = agentChatModels.find(
    (model) => model.id === selectedChatModel,
  );

  return (
    <motion.div
      key="overview"
      className="max-w-3xl mx-auto md:mt-20"
      initial={{ opacity: 0, scale: 0.98 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.98 }}
      transition={{ delay: 0.5 }}
    >
      <div className="rounded-xl p-6 flex flex-col gap-8 leading-relaxed text-center max-w-xl">
        <p>
          {selectedChatModel === MODERATOR_CHAT_MODEL.id ? (
            <>{t('moderator_welcome')}</>
          ) : (
            <>{t('default_description')}</>
          )}
        </p>
      </div>
    </motion.div>
  );
};
