import { getTranslations } from 'next-intl/server';

type Locale = 'ru' | 'en' | 'de';

export async function sendVerificationRequest(params) {
  const { identifier: to, provider, url, request } = params;
  const { host } = new URL(url);

  // Получаем локаль из cookies
  const locale = getLocaleFromCookies(request) || 'en';
  const t = await getTranslations({ locale, namespace: 'auth' });

  const res = await fetch('https://api.resend.com/emails', {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${provider.apiKey}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      from: provider.from,
      to,
      subject: `${t('email_template.sign_in_to')} ${host}`,
      html: html({ url, host, t }),
      text: text({ url, host, t }),
    }),
  });

  if (!res.ok)
    throw new Error(`Resend error: ${JSON.stringify(await res.json())}`);
}

// Функция для получения локали из cookies
function getLocaleFromCookies(request: Request): Locale | null {
  if (!request || !request.headers) {
    return null;
  }

  const cookies = request.headers.get('cookie') || '';

  // Преобразуем строку cookies в объект для удобства
  const cookieObject = Object.fromEntries(
    cookies
      .split(';')
      .map((cookie) => cookie.trim())
      .filter(Boolean)
      .map((cookie) => {
        const [key, ...rest] = cookie.split('=');
        return [key, rest.join('=')];
      }),
  );

  const cookieLocale = cookieObject?.NEXT_LOCALE;

  // Проверяем, что локаль является одной из поддерживаемых
  if (cookieLocale === 'ru' || cookieLocale === 'en' || cookieLocale === 'de') {
    return cookieLocale as Locale;
  }

  return null;
}

function html(params: {
  url: string;
  host: string;
  t: any;
}) {
  const { url, host, t } = params;

  const brandColor = '#3b4a9a';
  const color = {
    background: '#f5f5f5',
    text: '#333333',
    mainBackground: '#ffffff',
    buttonBackground: '#f4d03f',
    buttonBorder: '#f4d03f',
    buttonText: '#333333',
    headerBackground: '#293C8B',
    footerText: '#666666',
  };

  return `
  <body style="background: ${color.background}; margin: 0; padding: 0; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
    <table width="100%" border="0" cellspacing="0" cellpadding="0" bgcolor="${color.background}">
      <tr>
        <td align="center" style="padding: 20px 0;">
          <table width="600" border="0" cellspacing="0" cellpadding="0" bgcolor="${color.mainBackground}" style="border-radius: 0; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);">
            <!-- Header with logo -->
            <tr>
              <td bgcolor="${color.headerBackground}" style="padding: 30px 40px; text-align: center;">
                <img src="https://bca-agent-8q4e.vercel.app/images/Logo.png" alt="Blue Card Agency" style="max-width: 200px; height: auto; display: block; margin: 0 auto;" />
              </td>
            </tr>
            <!-- Main content -->
            <tr>
              <td style="padding: 40px;">
                <h1 style="color: ${color.text}; font-size: 24px; margin: 0 0 20px; font-weight: normal;">${t('email_template.custom_header')}</h1>
                <p style="color: ${color.text}; font-size: 16px; margin: 0 0 30px; line-height: 1.5;">${t('email_template.sign_in_to')}</p>
              </td>
            </tr>
            <tr>
              <td align="center">
                <a href="${url}" target="_blank" style="display: inline-block; color: ${color.buttonText}; text-decoration: none; font-weight: bold; font-size: 16px; background-color: ${color.buttonBackground};border-radius: 6px; padding: 15px 30px;">${t('email_template.sign_in_button')}</a>
              </td>
            </tr>
            <tr>
              <td style="padding: 40px;">
                <p style="color: ${color.text}; font-size: 14px; margin: 30px 0 0; line-height: 1.5;">${t('email_template.link_expires')}</p>
                <p style="color: ${color.text}; font-size: 14px; margin: 20px 0 0; line-height: 1.5;">${t('email_template.ignore_message')}</p>
                
                <!-- Footer -->
                <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #eeeeee;">
                  <p style="color: ${color.footerText}; font-size: 14px; margin: 0; font-weight: bold;">${t('email_template.regards')}</p>
                  <p style="color: ${color.footerText}; font-size: 14px; margin: 5px 0 0; font-weight: bold;">${t('email_template.team_signature')}</p>
                </div>
              </td>
            </tr>
          </table>
        </td>
      </tr>
    </table>
  </body>
  `;
}

// Email Text body (fallback for email clients that don't render HTML, e.g. feature phones)
function text({ url, host, t }: { url: string; host: string; t: any }) {
  return `${t('email_template.custom_header')}\n\n${t('email_template.sign_in_to')} ${host}\n\n${url}\n\n${t('email_template.ignore_message')}\n`;
}
