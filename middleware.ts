import type { NextRequest } from 'next/server';
import type { NextResponse } from 'next/server';
import createMiddleware from 'next-intl/middleware';
import NextAuth from 'next-auth';
import { authConfig } from '@/app/(auth)/auth.config';

const intlMiddleware = createMiddleware({
  locales: ['en', 'ru'],
  defaultLocale: 'en',
  localePrefix: 'always',
  pages: {
    '*': ['common'],
    '/(auth)/login': ['auth'],
    '/(auth)/register': ['auth'],
    '/(chat)/chat/[id]': ['chat'],
  },
});

//import {loadMcpConfig} from '@/lib/mcp/load.config';
export async function middleware(req: NextRequest) {
  const { pathname } = req.nextUrl;

  if (
    pathname.startsWith('/api') ||
    pathname.startsWith('/_next') ||
    pathname.match(/\.[^/]+$/)
  ) {
    return NextAuth(authConfig).auth(req) as NextResponse;
  }

  const cookieLocale = req.cookies.get('NEXT_LOCALE')?.value;
  const locale =
    cookieLocale && ['en', 'ru', 'de'].includes(cookieLocale)
      ? cookieLocale
      : 'en';

  // 1. Выполняем intl middleware
  const intlResponse = intlMiddleware(req);
  if (intlResponse) {
    // 2. Авторизация должна быть вложена — оборачиваем результат
    return NextAuth(authConfig).auth(req, intlResponse) as NextResponse;
  }

  // 3. Если intl не отработал (маловероятно), всё равно вызываем авторизацию
  return NextAuth(authConfig).auth(req) as NextResponse;
}

export const config = {
  matcher: ['/:id', '/chat/:id', '/login', '/register', '/error', '/sent', '/'],
};
