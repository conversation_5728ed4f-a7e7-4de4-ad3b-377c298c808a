import React from 'react';
import Form from 'next/form';

import { Input } from './ui/input';
import { Label } from './ui/label';
import Link from 'next/link';
import { useTranslations } from 'next-intl';

export function AuthForm({
  action,
  children,
  defaultEmail = '',
  offerAccepted,
  setOfferAccepted,
  toggleMagicLinkForm,
  isRegister = false,
}: {
  action: NonNullable<
    string | ((formData: FormData) => void | Promise<void>) | undefined
  >;
  children: React.ReactNode;
  defaultEmail?: string;
  offerAccepted?: boolean;
  setOfferAccepted?: (value: boolean) => void;
  toggleMagicLinkForm?: () => void;
  isRegister?: boolean;
}) {
  const translation_login_page = useTranslations('login_page');
  const translation_terms_use = useTranslations('agreements');

  const [showPassword, setShowPassword] = React.useState(false);

  const handleShowPassword = () => {
    setShowPassword(!showPassword);
  };

  return (
    <Form
      action={action}
      className="flex flex-col gap-4 px-4 sm:px-16 text-gray-300"
    >
      <div className="flex flex-col gap-2">
        <Label htmlFor="email" className="font-normal text-xs md:text-sm">
          {translation_login_page('email')}
        </Label>

        <Input
          id="email"
          name="email"
          className="bg-muted text-md md:text-sm text-black"
          type="email"
          placeholder="<EMAIL>"
          autoComplete="email"
          required
          autoFocus
          defaultValue={defaultEmail}
        />
      </div>
      <div className="flex flex-col gap-2">
        <Label htmlFor="password" className="font-normal text-xs md:text-sm">
          {translation_login_page('password')}
        </Label>

        <div className="relative flex">
          <Input
            id="password"
            name="password"
            className="bg-muted text-md md:text-sm pr-[25px] text-black"
            type={showPassword ? 'text' : 'password'}
            required
          />
          <button
            type="button"
            className="absolute top-[8px] right-[8px]"
            onClick={handleShowPassword}
          >
            {showPassword ? (
              <img
                src="/images/hide-icon.png"
                alt="Eye"
                className="w-[26px] h-[26px] drop-shadow-[2px_1px_1px_rgba(0,0,0,0.7)] hover:drop-shadow-[-2px_1px_1px_rgba(0,0,0,0.7)] hover:scale-95"
              />
            ) : (
              <img
                src="/images/show-icon.png"
                alt="Eye"
                className="w-[26px] h-[26px] drop-shadow-[2px_1px_1px_rgba(0,0,0,0.7)] hover:drop-shadow-[-2px_1px_1px_rgba(0,0,0,0.7)] hover:scale-95"
              />
            )}
          </button>
        </div>

        {!isRegister && (
          <button
            type="button"
            className="flex justify-end text-xs italic text-sidebar-accent font-bold hover:underline cursor-pointer hover:text-white"
            onClick={() => toggleMagicLinkForm?.()}
          >
            {translation_login_page('password_recovery')}
          </button>
        )}
      </div>
      {setOfferAccepted && (
        <div className="flex items-center gap-2 my-4">
          <input
            type="checkbox"
            id="offer"
            checked={offerAccepted}
            onChange={(e) => setOfferAccepted?.(e.target.checked)}
            className="accent-primary transform scale-125"
            required
          />
          <Label htmlFor="offer" className="text-xs cursor-pointer mt-0">
            {translation_terms_use('label_agreements')}{' '}
            <Link
              href="#"
              className="text-xs font-semibold text-white hover:underline"
            >
              {translation_terms_use('link_agreements')}
            </Link>
          </Label>
        </div>
      )}
      {children}
    </Form>
  );
}
