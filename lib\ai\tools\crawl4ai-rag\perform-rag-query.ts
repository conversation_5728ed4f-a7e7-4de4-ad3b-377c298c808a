import { z } from 'zod';
import { initMcpClient, mcpClient } from './mcp-client';
import { tool, type DataStreamWriter } from 'ai';
import type { Session } from 'next-auth';

interface PerformRagQueryProps {
  session: Session;
  dataStream: DataStreamWriter;
}

const performRagQuerySchema = z.object({
  query: z.string().describe('Поисковый запрос для RAG'),
  source: z
    .string()
    .optional()
    .describe(
      'Опциональный источник для фильтрации результатов (например, "www.gesetze-im-internet.de")',
    ),
  match_count: z
    .number()
    .optional()
    .default(5)
    .describe(
      'Максимальное количество результатов для возврата (по умолчанию: 5)',
    ),
});

export const performRagQuery = ({
  session,
  dataStream,
}: PerformRagQueryProps) =>
  tool({
    description:
      'Выполнить поисковый запрос по базе знаний для получения детальной информации о миграционных законах, процедурах и требованиях. Используйте параметр source для уточнения источника (например, "www.gesetze-im-internet.de" для законодательства или "crm.bluecardagency.de" для процедур Blue Card).',
    parameters: performRagQuerySchema,
    execute: async ({ query, source, match_count }) => {
      if (!session?.user) {
        dataStream.writeData({
          type: 'text',
          content: 'Unauthorized',
        });

        return 'Unauthorized';
      }

      dataStream.writeData({
        type: 'text',
        content: 'Выполняю запрос к RAG серверу...',
      });

      try {
        const result = await _callMcpPerformRagQuery({
          query,
          source,
          match_count,
        });

        if (!result.success) {
          const errorMessage = `Ошибка при выполнении запроса к RAG серверу: ${result.error}`;
          // dataStream.writeData({
          //   type: 'text',
          //   content: errorMessage,
          // });
          return result;
        }

        // Завершаем поток данных
        dataStream.writeData({ type: 'finish', content: '' });

        // Возвращаем строку с текстом, как в других инструментах
        return result;
      } catch (error) {
        console.error(`Error calling MCP _callMcpPerformRagQuery: ${error}`);

        return { success: false, error: `MCP RAG Client Error: ${error}` };
      }
    },
  });

async function _callMcpPerformRagQuery({
  query,
  source,
  match_count = 5,
}: z.infer<typeof performRagQuerySchema>): Promise<{
  success: boolean;
  data?: Array<{
    url: string;
    content: string;
    metadata: Record<string, any>;
    score: number;
  }>;
  error?: string;
}> {
  // Просто используем оригинальный запрос без модификаций
  // Перевод и формулировка запросов на немецком будет выполняться в промпте
  if (!mcpClient) {
    try {
      await initMcpClient();
    } catch (error) {
      console.error('[performRagQuery] Failed to initialize MCP client:', error);
      return { 
        success: false, 
        error: `MCP RAG client initialization failed: ${error}. RAG functionality is temporarily unavailable.` 
      };
    }
  }

  try {
    if (!mcpClient) {
      const errorMsg =
        'MCP RAG client not configured (MCP_RAG_SERVER_URL environment variable is missing). Cannot perform RAG query.';
      console.error(errorMsg);
      return { success: false, error: errorMsg };
    }

    // Подготавливаем параметры запроса
    const args: Record<string, unknown> = {
      query,
      match_count,
    };

    // Добавляем источник, если он указан
    if (source) {
      args.source = source;
    }

    console.log(
      `[MCP RAG Client] Calling perform_rag_query with query: "${query}", source: ${source || 'all'}, match_count: ${match_count}`,
    );

    type MCPResponse = {
      content?: Array<{ type: string; text?: string }>;
      isError?: boolean;
      [key: string]: unknown;
    };

    // Добавляем таймаут для запроса
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(
        () => reject(new Error('Timeout waiting for MCP RAG response')),
        60000, // Увеличиваем таймаут для RAG-запросов, так как они могут занимать больше времени
      );
    });

    // Используем оригинальный запрос
    args.query = query;
    let response: unknown;
    try {
      const toolCallPromise = mcpClient.callTool({
        name: 'perform_rag_query',
        arguments: args,
      });

      // Используем Promise.race для ограничения времени запроса
      response = await Promise.race([timeoutPromise, toolCallPromise]);
    } catch (error) {
      console.error(
        '[MCP RAG Client] Error calling MCP mcpClient.callTool:',
        error,
      );
      return { success: false, error: `MCP RAG Client Error: ${error}` };
    }

    const typedResponse = response as MCPResponse;
    const resultText = Array.isArray(typedResponse?.content)
      ? typedResponse.content.find((c) => c.type === 'text')?.text
      : undefined;

    if (typedResponse?.isError || !resultText) {
      const errorMessage = `RAG MCP Error: ${resultText || 'Unknown error from MCP server'}`;
      console.error(`[MCP RAG Client] Error response: ${errorMessage}`);
      return { success: false, error: errorMessage };
    }

    try {
      // Пытаемся распарсить JSON-ответ
      const parsedData = JSON.parse(resultText);

      // Проверяем, что ответ содержит ожидаемую структуру
      if (parsedData.success === false) {
        return {
          success: false,
          error: parsedData.error || 'Неизвестная ошибка в ответе RAG',
        };
      }

      if (Array.isArray(parsedData.results)) {
        return {
          success: true,
          data: parsedData.results,
        };
      } else if (parsedData.success === true) {
        // Если нет результатов, но запрос успешен
        return {
          success: true,
          data: [],
        };
      }

      // Если структура ответа не соответствует ожиданиям
      return {
        success: false,
        error: 'Неожиданный формат ответа от RAG сервера',
      };
    } catch (parseError) {
      console.error(
        '[MCP RAG Client] Error parsing JSON response:',
        parseError,
      );
      return {
        success: false,
        error: `Ошибка при обработке ответа RAG: ${parseError instanceof Error ? parseError.message : 'Неизвестная ошибка'}`,
      };
    }
  } catch (error: unknown) {
    console.error(
      '[MCP RAG Client] Error calling MCP perform_rag_query:',
      error,
    );
    const message =
      error instanceof Error
        ? error.message
        : 'Unknown error during MCP RAG call';

    try {
      await initMcpClient();
    } catch (closeError) {
      console.error('[MCP RAG Client] Error closing MCP client:', closeError);
    }

    return { success: false, error: `MCP RAG Client Error: ${message}` };
  }
}
