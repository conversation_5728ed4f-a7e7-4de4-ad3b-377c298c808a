import {
  appendResponseMessages,
  createDataStreamResponse,
  streamText,
  type UIMessage,
} from 'ai';
import { auth } from '@/app/(auth)/auth';
import { systemPrompt } from '@/lib/ai/prompts';
import {
  deleteChatById,
  getChatById,
  saveChat,
  saveMessages,
} from '@/lib/db/queries';
import {
  generateUUID,
  getMostRecentUserMessage,
  getTrailingMessageId,
} from '@/lib/utils';
import { generateTitleFromUserMessage } from '../../actions';
import { isProductionEnvironment } from '@/lib/constants';
import { myProvider } from '@/lib/ai/providers';
import { createRagService, RagServiceTypes } from '@/lib/rag/service';
import type {
  AiSearchResponse,
  CloudflareSettings,
} from '@/lib/rag/cloudflare/cfaisearch.types';
import { MODERATOR_CHAT_MODEL } from '@/lib/ai/models';
import {
  getActiveToolsForRole,
  getToolsObjectForRole,
  disconnectAllMcpServices,
} from '@/lib/ai/tools';
import type { UserRole } from '@/app/(auth)/auth.config';

export const maxDuration = 60;

export async function POST(request: Request) {
  try {
    const {
      id,
      messages,
      selectedChatModel,
    }: {
      id: string;
      messages: Array<UIMessage>;
      selectedChatModel: string;
    } = await request.json();

    const session = await auth();

    if (!session || !session.user || !session.user.id) {
      return new Response('Unauthorized', { status: 401 });
    }

    console.log('session.user.role', session.user.role);

    const userMessage = getMostRecentUserMessage(messages);

    if (!userMessage) {
      return new Response('No user message found', { status: 400 });
    }

    let chat_RAG: UIMessage | undefined = undefined;
    const feature_rag = process.env.WITH_RAG;
    const rag_check = feature_rag === 'true';
    if (rag_check) {
      const rag_service = createRagService(RagServiceTypes.CLOUDFLARE, <
        CloudflareSettings
      >{
        apiToken: process.env.CLOUDFLARE_API_TOKEN,
        accountId: process.env.CLOUDFLARE_ACCOUNT_ID,
        autoragName: process.env.CLOUDFLARE_RAG_NAME,
      });
      const rag_response: AiSearchResponse = await rag_service.findSimilarDocs(
        userMessage.content,
      );
      const full_result_search_info = rag_response.result.response;
      chat_RAG = {
        content: full_result_search_info,
        id: generateUUID(),
        role: 'system',
        parts: [{ type: 'text', text: full_result_search_info }],
        createdAt: new Date(),
      };
    }

    const chat = await getChatById({ id });

    if (!chat) {
      const title = await generateTitleFromUserMessage({
        message: userMessage,
      });

      await saveChat({
        id,
        userId: session.user.id,
        title,
        agentModel: selectedChatModel,
      });
    } else {
      if (chat.userId !== session.user.id) {
        return new Response('Unauthorized', { status: 401 });
      }
    }

    await saveMessages({
      messages: [
        {
          chatId: id,
          id: userMessage.id,
          role: 'user',
          parts: userMessage.parts,
          attachments: userMessage.experimental_attachments ?? [],
          createdAt: new Date(),
        },
      ],
    });

    if (rag_check) messages.push(<UIMessage>chat_RAG);

    const cookies = request.headers.get('cookie') || '';

    // Если нужно преобразовать строку cookies в объект для удобства
    const cookieObject = Object.fromEntries(
      cookies
        .split(';')
        .map((cookie) => cookie.trim())
        .filter(Boolean)
        .map((cookie) => {
          const [key, ...rest] = cookie.split('=');
          return [key, rest.join('=')];
        }),
    );
    const cookieLocale = cookieObject?.NEXT_LOCALE || 'en';

    console.log(cookieLocale);

    const role = session.user.role as UserRole;

    // Получаем инструменты в зависимости от роли пользователя
    console.log('[API Chat] Getting active tools for role:', role);
    const activeTools = await getActiveToolsForRole(role);

    console.log('User role:', role);
    console.log('Active tools for role:', activeTools);

    console.log('[API Chat] Creating data stream response...');
    return createDataStreamResponse({
      execute: async (dataStream) => {
        console.log('[API Chat] Getting tools object for role...');
        const toolsObject = await getToolsObjectForRole(
          role,
          session,
          dataStream,
        );
        console.log('[API Chat] Tools object keys:', Object.keys(toolsObject));

        console.log('[API Chat] Starting streamText...');
        const result = streamText({
          model: myProvider.languageModel(selectedChatModel),
          system: systemPrompt({ lang: cookieLocale, selectedChatModel }),
          messages,
          maxSteps: 10,
          experimental_activeTools: activeTools,
          experimental_generateMessageId: generateUUID,
          tools: toolsObject,
          experimental_toolCallStreaming: true,
          onStepFinish: ({ stepType, toolCalls, toolResults }) => {
            console.log('[API Chat] Step finished:', stepType);
            if (toolCalls) {
              console.log('[API Chat] Tool calls:', toolCalls.map(tc => ({ name: tc.toolName, args: tc.args })));
            }
            if (toolResults) {
              console.log('[API Chat] Tool results:', toolResults.map(tr => ({ 
                toolCallId: tr.toolCallId, 
                result: typeof tr.result === 'object' ? JSON.stringify(tr.result).substring(0, 200) : tr.result 
              })));
            }
          },
          onError: (error) => {
            console.error('[API Chat] Error in streamText:', error);
            console.error('[API Chat] Error stack:', error instanceof Error ? error.stack : 'No stack trace');
          },
          onFinish: async ({ response }) => {
            if (session.user?.id) {
              try {
                const assistantId = getTrailingMessageId({
                  messages: response.messages.filter(
                    (message) => message.role === 'assistant',
                  ),
                });

                if (!assistantId) {
                  throw new Error('No assistant message found!');
                }

                const [, assistantMessage] = appendResponseMessages({
                  messages: [userMessage],
                  responseMessages: response.messages,
                });

                await saveMessages({
                  messages: [
                    {
                      id: assistantId,
                      chatId: id,
                      role: assistantMessage.role,
                      parts: assistantMessage.parts,
                      attachments:
                        assistantMessage.experimental_attachments ?? [],
                      createdAt: new Date(),
                    },
                  ],
                });
                disconnectAllMcpServices();
              } catch (_) {
                console.error('Failed to save chat');
                disconnectAllMcpServices();
              }
            }
          },
          experimental_telemetry: {
            isEnabled: isProductionEnvironment,
            functionId: 'stream-text',
          },
        });

        result.consumeStream();

        result.mergeIntoDataStream(dataStream, {
          sendReasoning: true,
        });
      },
      onError: (error) => {
        console.error('[API Chat] Error in data stream:', error);
        console.error('[API Chat] Error details:', error instanceof Error ? error.stack : 'No stack trace');
        
        // Отключаем MCP сервисы при ошибке
        try {
          disconnectAllMcpServices();
        } catch (disconnectError) {
          console.error('[API Chat] Error disconnecting MCP services in onError:', disconnectError);
        }
        
        return 'Oops, an error occurred!';
      },
    });
  } catch (error) {
    console.error('[API Chat] Error in POST handler:', error);
    console.error('[API Chat] Error stack:', error instanceof Error ? error.stack : 'No stack trace');
    
    // Убеждаемся, что MCP сервисы отключены при ошибке
    try {
      disconnectAllMcpServices();
    } catch (disconnectError) {
      console.error('[API Chat] Error disconnecting MCP services:', disconnectError);
    }
    
    return new Response('An error occurred while processing your request!', {
      status: 500,
    });
  }
}

export async function DELETE(request: Request) {
  const { searchParams } = new URL(request.url);
  const id = searchParams.get('id');

  if (!id) {
    return new Response('Not Found', { status: 404 });
  }

  const session = await auth();

  if (!session || !session.user) {
    return new Response('Unauthorized', { status: 401 });
  }

  try {
    const chat = await getChatById({ id });

    if (chat.userId !== session.user.id) {
      return new Response('Unauthorized', { status: 401 });
    }

    await deleteChatById({
      id,
      onlyClearMessages: chat.agentModel === MODERATOR_CHAT_MODEL.id,
    });

    return new Response('Chat deleted', { status: 200 });
  } catch (error) {
    return new Response('An error occurred while processing your request!', {
      status: 500,
    });
  }
}
