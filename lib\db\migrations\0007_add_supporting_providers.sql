CREATE TABLE IF NOT EXISTS public.account (
  userId UUID NOT NULL,
  type TEXT NOT NULL,
  provider TEXT NOT NULL,
  providerAccountId TEXT NOT NULL,
  refresh_token TEXT,
  access_token TEXT,
  expires_at INTEGER,
  token_type TEXT,
  scope TEXT,
  id_token TEXT,
  session_state TEXT,
  PRIMARY KEY (provider, providerAccountId),
  FOREIG<PERSON> KEY (userId) REFERENCES public."User"(id) ON DELETE CASCADE
);

CREATE TABLE IF NOT EXISTS public.session (
  sessionToken TEXT PRIMARY KEY,
  userId UUID NOT NULL,
  expires TIMESTAMP NOT NULL,
  <PERSON>OR<PERSON><PERSON><PERSON> KEY (userId) REFERENCES public."User"(id) ON DELETE CASCADE
);

CREATE TABLE IF NOT EXISTS public.verificationToken (
  identifier TEXT NOT NULL,
  token TEXT NOT NULL,
  expires TIMESTAMP NOT NULL,
  PRIMARY KEY (identifier, token)
);

DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name='User' AND column_name='name'
    ) THEN
        ALTER TABLE public."User" ADD COLUMN name VARCHAR(255);
    END IF;
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name='User' AND column_name='emailVerified'
    ) THEN
        ALTER TABLE public."User" ADD COLUMN emailVerified TIMESTAMPTZ;
    END IF;
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name='User' AND column_name='image'
    ) THEN
        ALTER TABLE public."User" ADD COLUMN image TEXT;
    END IF;
END $$;
