import type { DefaultUser } from 'next-auth';

/**
 * We extend the default user and session types of next-auth to include
 * the role field. This allows us to use the role in our auth callbacks.
 *
 * See https://next-auth.js.org/getting-started/typescript#extending-user-type
 */

declare module 'next-auth' {
  interface User extends DefaultUser {
    role?: UserRole;
  }
  interface Session {
    user: {
      id: string;
      name?: string | null;
      email?: string | null;
      image?: string | null;
      role?: UserRole;
    };
  }
}
